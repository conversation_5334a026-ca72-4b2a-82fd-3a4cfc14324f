package id.co.bri.brimo.ui.activities.bukarekening

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.widget.Button
import android.widget.RelativeLayout
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import butterknife.Bind
import butterknife.ButterKnife
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import com.tbuonomo.viewpagerdotsindicator.DotsIndicator
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.britamarencanarevamp.IPilihTabunganRevPresenter
import id.co.bri.brimo.contract.IView.britamarencanarevamp.IPilihTabunganRevView
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.ParameterPilihKantorModel
import id.co.bri.brimo.models.apimodel.request.bukarekening.InquiryOpenAccountRequest
import id.co.bri.brimo.models.apimodel.response.onExceptionWH
import id.co.bri.brimo.models.apimodel.response.MessageResponse
import id.co.bri.brimo.models.apimodel.response.QuestionResponse
import id.co.bri.brimo.models.apimodel.response.bukarekening.FormValasResponse
import id.co.bri.brimo.models.apimodel.response.bukarekening.InquiryOpenAccJunioResponse
import id.co.bri.brimo.models.apimodel.response.bukarekening.InquiryOpenAccResponse
import id.co.bri.brimo.models.apimodel.response.bukarekening.JenisTabunganResponse
import id.co.bri.brimo.models.apimodel.response.rencanarev.InquiryRencanaResponse
import id.co.bri.brimo.ui.activities.DetailPusatBantuanActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.britamarencana.HitungRencanaActivity
import id.co.bri.brimo.ui.activities.bukarekening.britamajuniorevamp.FormBritamaJunioRevActivity
import id.co.bri.brimo.ui.activities.bukarekening.britamarencanarevamp.FormBritamaRencanaActivity
import id.co.bri.brimo.ui.activities.bukarekening.bukavalasrevamp.FormBukaValasRevActivity
import id.co.bri.brimo.ui.customviews.NonSwipeableViewPagerOnOff
import id.co.bri.brimo.ui.customviews.dialog.DialogRekeningDiproses
import id.co.bri.brimo.ui.customviews.dialog.DialogWorkingHours
import id.co.bri.brimo.ui.fragments.BottomFragmentSafteyMode
import id.co.bri.brimo.ui.fragments.FragmentDialogNoImageRevamp
import id.co.bri.brimo.ui.fragments.bukarekening.TabunganCardFragment
import javax.inject.Inject


class TabunganActivity : BaseActivity(), IPilihTabunganRevView, View.OnClickListener,FragmentDialogNoImageRevamp.DialogDefaultListener,TabunganCardFragment.DialogDefaultListener,
    DialogRekeningDiproses.DialogDefaultListener,BottomFragmentSafteyMode.DialogDefaulListener,DialogWorkingHours.DialogDefaultListener {

    @Bind(R.id.vp_tabungan)
    lateinit var vpTabungan : NonSwipeableViewPagerOnOff
    @Bind(R.id.dots_indicator)
    lateinit var dotsIndicator:DotsIndicator
    @Bind(R.id.toolbar)
    lateinit var toolbar: Toolbar
    @Bind(R.id.btn_rekening_baru)
    lateinit var btnRekeningBaru : Button
    @Bind(R.id.rlCard)
    lateinit var rlCard : RelativeLayout



    @Inject
    lateinit var presenter: IPilihTabunganRevPresenter<IPilihTabunganRevView?>

    var mProduct = JenisTabunganResponse.Product()
    var productList: List<JenisTabunganResponse.Product> = ArrayList()
    private var adapterViewPager: FragmentStatePagerAdapter? = null
    var skeletonScreen: SkeletonScreen? = null
    var mPosition : Int = 0

    var mUrlInquiry : String = ""
    var murlKonfirmasi : String = ""
    var urlLokasiSendiri : String = ""
    var urlLokasiPencarian : String = ""
    var mUrlPayment : String = ""
    var messageResponse: MessageResponse? = null

    var fragmentDialogNoImageRevamp: FragmentDialogNoImageRevamp? = null
    var bottomFragmentSafteyMode : BottomFragmentSafteyMode? = null
    var dialogWorkingHours :DialogWorkingHours? = null

    private val debounceHandler = Handler(Looper.getMainLooper())
    private var isClickable = true

    companion object {
        var mProductType: String? = null
        var mFromProductRecomendetion :Boolean = false
        var mNamaTabungan = ""
        fun launchIntent(caller: Activity) {
            val intent = Intent(caller, TabunganActivity::class.java)
            mNamaTabungan =""
            caller.startActivityForResult(intent, Constant.REQ_BUKA_REKENING)
        }

        fun launchIntentTabungan(caller: Activity, namaTabungan : String) {
            val intent = Intent(caller, TabunganActivity::class.java)
            mNamaTabungan= namaTabungan
            caller.startActivityForResult(intent, Constant.REQ_BUKA_REKENING)
        }

        fun launchIntentRecomendation(
            caller: Activity,
            productType: String?,
            fromReccomendetion: Boolean
        ) {
            val intent = Intent(caller, TabunganActivity::class.java)
            mProductType = productType
            mFromProductRecomendetion = fromReccomendetion
            mNamaTabungan =""
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_tabungan)
        ButterKnife.bind(this)

        GeneralHelper.setToolbarRevamp(this, toolbar, GeneralHelper.getString(R.string.toolbar_tabungan))

        injectDependecy()
        setupViews()
    }

    fun injectDependecy() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrl(GeneralHelper.getString(R.string.url_buka_tabungan_revamp))
        presenter.setUrlSafety(GeneralHelper.getString(R.string.url_safety_pusat_bantuan))
        presenter.getJenisTabungan()
    }

     fun setupViews() {
         setStatusColorAndStatusBar(R.color.highlightColor, View.SYSTEM_UI_FLAG_VISIBLE)
         urlLokasiSendiri = GeneralHelper.getString(R.string.url_s3f_lokasi_sendiri)
         urlLokasiPencarian = GeneralHelper.getString(R.string.url_s3f_lokasi_pencarian)
         btnRekeningBaru.setOnClickListener(this)
    }


    override fun onSuccessGetData(jenisTabunganResponse: JenisTabunganResponse?) {
        vpTabungan.setSwipeEnabled(true)
        dotsIndicator.visibility = View.VISIBLE
        jenisTabunganResponse?.produks?.let { products->
            productList = products
            if (!mProductType.isNullOrEmpty()) {
                for (i in productList.indices) {
                    if (productList[i].productType.equals(mProductType)) {
                        ProductBriefBukaRekeningActivity.launchIntent(this, productList, i)
                    }
                }
            }
        }


        adapterViewPager = MyPagerAdapter(supportFragmentManager, productList, this, mProductType, mFromProductRecomendetion)

        vpTabungan.adapter = adapterViewPager
        vpTabungan.pageMargin = 100

        var mProductPosition: Int? = 0

        if (mNamaTabungan.isNotEmpty()){
            for (i in productList.indices) {
                if (productList[i].type == mNamaTabungan) {
                    mProductPosition = i
                }
            }
            if (mNamaTabungan == Constant.OPEN_ACCOUNT_VALAS || mNamaTabungan == Constant.OPEN_ACCOUNT_RENCANA) {
                if (mProductPosition != null) {
                    vpTabungan.setCurrentItem(mProductPosition, true)
                    vpTabungan.setSwipeEnabled(false)
                    dotsIndicator.visibility = View.GONE
                }
            }
        }


        dotsIndicator.setViewPager(vpTabungan)

    }

    override fun onSuccessInquiry(inquiryGeneralOpenAccountResponse: InquiryOpenAccResponse) {
        InquiryProductTabunganActivity.launchIntent(
            this, productList[mPosition], inquiryGeneralOpenAccountResponse,murlKonfirmasi,mUrlPayment)
    }

    override fun onSuccessPusatBantuanSafety(questionResponse: QuestionResponse) {
        DetailPusatBantuanActivity.launchIntent(this, questionResponse, questionResponse.getTopicName());
    }


    override fun onException03(onExceptionWH: onExceptionWH?) {
        messageResponse = onExceptionWH!!.progresContent
        val dialogValas = DialogRekeningDiproses(this, messageResponse!!.title, messageResponse!!.description)
        val ft = this.supportFragmentManager.beginTransaction()
        ft.add(dialogValas, null)
        ft.commitAllowingStateLoss()
    }

    override fun onException02(onExceptionWH: onExceptionWH?) {
        messageResponse = onExceptionWH!!.workingHoursResponse
        dialogWorkingHours = DialogWorkingHours.newInstance(
                this,
                messageResponse!!.title,
                messageResponse!!.description,
                GeneralHelper.getString(R.string.ok)
        )
        dialogWorkingHours!!.show(supportFragmentManager, "")
    }

    override fun showSkeleton() {
        skeletonScreen = Skeleton.bind(rlCard)
            .shimmer(true)
            .angle(20)
            .duration(1200).color(R.color.white)
            .load(R.layout.skeleton_tabungan)
            .show()
    }

    override fun hideSkeleton() {
        skeletonScreen!!.hide()
    }

    override fun disableButton(disable: Boolean) {
        if (!disable) {
            setButton(true)
        } else {
            setButton(false)
        }

    }

    override fun onSuccesFormValas(formValasResponse: FormValasResponse) {
        FormBukaValasRevActivity.launchIntent(this,productList[mPosition], formValasResponse)
    }

    override fun onSuccessInquiryRencana(rencana: InquiryRencanaResponse?) {
        rencana?.let {
            FormBritamaRencanaActivity.launchIntent(
                    this, mProduct, it,murlKonfirmasi,mUrlPayment)
        }
    }


    override fun onClick(p0: View?) {
        if (isClickable) {
            isClickable = false
            // Handle your click logic here

            // Set a delay before allowing the next click
            debounceHandler.postDelayed({
                isClickable = true
                when (mProduct.type) {
                    Constant.OPEN_ACCOUNT_GENERAL ->
                        if (mProduct.limited){
                            fragmentDialogNoImageRevamp = FragmentDialogNoImageRevamp.newInstance(
                                    this,
                                    mProduct.limitedContent.title,
                                    mProduct.limitedContent.description,
                                    GeneralHelper.getString(R.string.txt_pilih_tabungan_lain),
                                    Constant.OPEN_ACCOUNT_GENERAL,
                                    true,
                                    false
                            )

                            fragmentDialogNoImageRevamp!!.show(supportFragmentManager, "");
                        }
                        else {
                            if (mProduct.safety == 2) {
                                fragmentDialogNoImageRevamp = FragmentDialogNoImageRevamp.newInstance(
                                        this,
                                        mProduct.safetyContent.title,
                                        mProduct.safetyContent.description,
                                        GeneralHelper.getString(R.string.txt_saya_mengerti),
                                        Constant.OPEN_ACCOUNT_GENERAL,
                                        false,
                                        true
                                )
                                fragmentDialogNoImageRevamp!!.show(supportFragmentManager, "");
                            }else if (mProduct.safety == 1){
                                bottomFragmentSafteyMode = BottomFragmentSafteyMode(
                                        this,
                                        mProduct.safetyContent.title,
                                        mProduct.safetyContent.description,
                                        GeneralHelper.getString(R.string.txt_saya_mengerti),
                                        GeneralHelper.getString(R.string.txt_pelajari_lebih_lanjut_rekening),
                                        Constant.OPEN_ACCOUNT_S3f
                                )
                                bottomFragmentSafteyMode!!.show(supportFragmentManager, "")
                            }
                            else{
                                mUrlInquiry = GeneralHelper.getString(R.string.url_buka_tabungan_inquiry_revamp)
                                murlKonfirmasi = GeneralHelper.getString(R.string.url_buka_tabungan_konfirmasi_revamp)
                                mUrlPayment = GeneralHelper.getString(R.string.url_buka_tabungan_payment_revamp)
                                presenter.setUrlInquiry(mUrlInquiry)
                                presenter.getInquiryTabungan(InquiryOpenAccountRequest(mProduct.productType))
                            }

                        }


                    Constant.OPEN_ACCOUNT_S3f ->
                        if (mProduct.limited){
                            fragmentDialogNoImageRevamp = FragmentDialogNoImageRevamp.newInstance(
                                    this,
                                    mProduct.limitedContent.title,
                                    mProduct.limitedContent.description,
                                    GeneralHelper.getString(R.string.txt_pilih_tabungan_lain),
                                    Constant.OPEN_ACCOUNT_S3f,
                                    true,
                                    false
                            )

                            fragmentDialogNoImageRevamp!!.show(supportFragmentManager, "");
                        }
                        else {
                            if (mProduct.safety == 2) {
                                fragmentDialogNoImageRevamp = FragmentDialogNoImageRevamp.newInstance(
                                        this,
                                        mProduct.safetyContent.title,
                                        mProduct.safetyContent.description,
                                        GeneralHelper.getString(R.string.txt_saya_mengerti),
                                        Constant.OPEN_ACCOUNT_S3f,
                                        false,
                                        true
                                )
                                fragmentDialogNoImageRevamp!!.show(supportFragmentManager, "");
                            }else if (mProduct.safety == 1){
                                bottomFragmentSafteyMode = BottomFragmentSafteyMode(
                                        this,
                                        mProduct.safetyContent.title,
                                        mProduct.safetyContent.description,
                                        GeneralHelper.getString(R.string.txt_saya_mengerti),
                                        GeneralHelper.getString(R.string.txt_pelajari_lebih_lanjut_rekening),
                                        Constant.OPEN_ACCOUNT_S3f
                                )
                                bottomFragmentSafteyMode!!.show(supportFragmentManager, "")
                            }
                            else{
                                mUrlInquiry = GeneralHelper.getString(R.string.url_buka_tabungan_s3f_inquiry_revamp)
                                murlKonfirmasi = GeneralHelper.getString(R.string.url_buka_tabungan_s3f_konfirmasi_revamp)
                                mUrlPayment = GeneralHelper.getString(R.string.url_buka_tabungan_s3f_payment_revamp)
                                presenter.setUrlInquiry(mUrlInquiry)
                                presenter.getInquiryTabungan(InquiryOpenAccountRequest(mProduct.productType))
                            }

                        }

                    Constant.OPEN_ACCOUNT_JUNIO -> {
                        if (mProduct.limited){
                            fragmentDialogNoImageRevamp = FragmentDialogNoImageRevamp.newInstance(
                                    this,
                                    mProduct.limitedContent.title,
                                    mProduct.limitedContent.description,
                                    GeneralHelper.getString(R.string.txt_pilih_tabungan_lain),
                                    Constant.OPEN_ACCOUNT_JUNIO,
                                    true,
                                    false
                            )

                            fragmentDialogNoImageRevamp!!.show(supportFragmentManager, "");

                        }
                        else {
                            if (mProduct.safety ==2) {
                                fragmentDialogNoImageRevamp = FragmentDialogNoImageRevamp.newInstance(
                                        this,
                                        mProduct.safetyContent.title,
                                        mProduct.safetyContent.description,
                                        GeneralHelper.getString(R.string.txt_saya_mengerti),
                                        Constant.OPEN_ACCOUNT_JUNIO,
                                        false,
                                        true
                                )
                                fragmentDialogNoImageRevamp!!.show(supportFragmentManager, "");
                            }
                            else if (mProduct.safety == 1){
                                bottomFragmentSafteyMode = BottomFragmentSafteyMode(
                                        this::onClickYes,
                                        mProduct.safetyContent.title,
                                        mProduct.safetyContent.description,
                                        GeneralHelper.getString(R.string.txt_saya_mengerti),
                                        GeneralHelper.getString(R.string.txt_pelajari_lebih_lanjut_rekening),
                                        Constant.OPEN_ACCOUNT_JUNIO
                                )
                                bottomFragmentSafteyMode!!.show(supportFragmentManager, "")
                            }
                            else{
                                mUrlInquiry = GeneralHelper.getString(R.string.url_buka_tabungan_junio_inquiry_revamp)
                                murlKonfirmasi = GeneralHelper.getString(R.string.url_buka_tabungan_junio_konfirmasi_revamp)
                                mUrlPayment = GeneralHelper.getString(R.string.url_buka_tabungan_junio_payment_revamp)
                                presenter.setUrlInquiry(mUrlInquiry)
                                presenter.getInquiryTabunganJunio(InquiryOpenAccountRequest(
                                        mProduct.productType))
                            }

                        }

                    }

                    Constant.OPEN_ACCOUNT_RENCANA ->
                        if (mProduct.limited){
                            fragmentDialogNoImageRevamp = FragmentDialogNoImageRevamp.newInstance(
                                    this,
                                    mProduct.limitedContent.title,
                                    mProduct.limitedContent.description,
                                    GeneralHelper.getString(R.string.txt_pilih_tabungan_lain),
                                    Constant.OPEN_ACCOUNT_RENCANA,
                                    true,
                                    false
                            )

                            fragmentDialogNoImageRevamp!!.show(supportFragmentManager, "");

                        }
                        else {
                            if (mProduct.safety ==2) {
                                fragmentDialogNoImageRevamp = FragmentDialogNoImageRevamp.newInstance(
                                        this,
                                        mProduct.safetyContent.title,
                                        mProduct.safetyContent.description,
                                        GeneralHelper.getString(R.string.txt_saya_mengerti),
                                        Constant.OPEN_ACCOUNT_RENCANA,
                                        false,
                                        true
                                )
                                fragmentDialogNoImageRevamp!!.show(supportFragmentManager, "");
                            }
                            else if (mProduct.safety == 1){
                                bottomFragmentSafteyMode = BottomFragmentSafteyMode(
                                        this::onClickYes,
                                        mProduct.safetyContent.title,
                                        mProduct.safetyContent.description,
                                        GeneralHelper.getString(R.string.txt_saya_mengerti),
                                        GeneralHelper.getString(R.string.txt_pelajari_lebih_lanjut_rekening),
                                        Constant.OPEN_ACCOUNT_RENCANA
                                )
                                bottomFragmentSafteyMode!!.show(supportFragmentManager, "")
                            }
                            else{
                                mUrlInquiry = GeneralHelper.getString(R.string.url_buka_tabungan_inquiry_rencana_revamp)
                                murlKonfirmasi = GeneralHelper.getString(R.string.url_buka_tabungan_konfirmasi_revamp)
                                mUrlPayment = GeneralHelper.getString(R.string.url_buka_tabungan_payment_revamp)
                                presenter.setUrlInquiry(mUrlInquiry)
                                presenter.getInquiryRencana(InquiryOpenAccountRequest(
                                        mProduct.productType))
                            }

                        }


                    Constant.OPEN_ACCOUNT_VALAS ->
                        if (mProduct.limited){
                            fragmentDialogNoImageRevamp = FragmentDialogNoImageRevamp.newInstance(
                                    this,
                                    mProduct.limitedContent.title,
                                    mProduct.limitedContent.description,
                                    GeneralHelper.getString(R.string.txt_pilih_tabungan_lain),
                                    Constant.OPEN_ACCOUNT_S3f,
                                    true,
                                    false
                            )

                            fragmentDialogNoImageRevamp!!.show(supportFragmentManager, "");
                        }
                        else {
                            if (mProduct.safety == 2) {
                                fragmentDialogNoImageRevamp = FragmentDialogNoImageRevamp.newInstance(
                                        this,
                                        mProduct.safetyContent.title,
                                        mProduct.safetyContent.description,
                                        GeneralHelper.getString(R.string.txt_saya_mengerti),
                                        Constant.OPEN_ACCOUNT_S3f,
                                        false,
                                        true
                                )
                                fragmentDialogNoImageRevamp!!.show(supportFragmentManager, "");
                            }else if (mProduct.safety == 1){
                                bottomFragmentSafteyMode = BottomFragmentSafteyMode(
                                        this,
                                        mProduct.safetyContent.title,
                                        mProduct.safetyContent.description,
                                        GeneralHelper.getString(R.string.txt_saya_mengerti),
                                        GeneralHelper.getString(R.string.txt_pelajari_lebih_lanjut_rekening),
                                        Constant.OPEN_ACCOUNT_S3f
                                )
                                bottomFragmentSafteyMode!!.show(supportFragmentManager, "")
                            }
                            else{
                                if (mProduct.safety == 2) {
                                    fragmentDialogNoImageRevamp = FragmentDialogNoImageRevamp.newInstance(
                                            this,
                                            mProduct.safetyContent.title,
                                            mProduct.safetyContent.description,
                                            GeneralHelper.getString(R.string.txt_saya_mengerti),
                                            Constant.OPEN_ACCOUNT_VALAS,
                                            false,
                                            true
                                    )
                                    fragmentDialogNoImageRevamp!!.show(supportFragmentManager, "");
                                }else if (mProduct.safety == 1){
                                    bottomFragmentSafteyMode = BottomFragmentSafteyMode(
                                            this,
                                            mProduct.safetyContent.title,
                                            mProduct.safetyContent.description,
                                            GeneralHelper.getString(R.string.txt_saya_mengerti),
                                            GeneralHelper.getString(R.string.txt_pelajari_lebih_lanjut_rekening),
                                            Constant.OPEN_ACCOUNT_VALAS
                                    )
                                    bottomFragmentSafteyMode!!.show(supportFragmentManager, "")
                                }
                                else{
                                    mUrlInquiry = GeneralHelper.getString(R.string.url_buka_tabungan_valas_form)
                                    presenter.setUrlFormValas(mUrlInquiry)
                                    presenter.getFormTabunganValas(InquiryOpenAccountRequest(mProduct.productType))
                                }

                            }

                        }

                    else -> {}
                }
            }, 500) // Adjust the delay as needed
        }
    }

    class MyPagerAdapter(
            fragmentManager: FragmentManager?,
            private val product1: List<JenisTabunganResponse.Product>, private val onListener : TabunganCardFragment.DialogDefaultListener, var mProductType: String?,
            var mFromProductRecomendetion : Boolean
    ) : FragmentStatePagerAdapter(fragmentManager!!) {

        // Returns total number of pages
        override fun getCount(): Int {
            return product1.size
        }

        // Returns the fragment to display for that page
        override fun getItem(position: Int): Fragment {
            return TabunganCardFragment.newInstance(onListener,product1,position,mProductType, mFromProductRecomendetion)
            }
    }

    override fun onClickYes(type: String?) {
        when (type) {
            Constant.OPEN_ACCOUNT_GENERAL ->  {
                mUrlInquiry = GeneralHelper.getString(R.string.url_buka_tabungan_inquiry_revamp)
                murlKonfirmasi = GeneralHelper.getString(R.string.url_buka_tabungan_konfirmasi_revamp)
                mUrlPayment = GeneralHelper.getString(R.string.url_buka_tabungan_payment_revamp)
                presenter.setUrlInquiry(mUrlInquiry)
                presenter.getInquiryTabungan(InquiryOpenAccountRequest(mProduct.productType))
            }
            Constant.OPEN_ACCOUNT_S3f ->  {
                mUrlInquiry = GeneralHelper.getString(R.string.url_buka_tabungan_s3f_inquiry_revamp)
                murlKonfirmasi = GeneralHelper.getString(R.string.url_buka_tabungan_s3f_konfirmasi_revamp)
                mUrlPayment = GeneralHelper.getString(R.string.url_buka_tabungan_s3f_payment_revamp)
                presenter.setUrlInquiry(mUrlInquiry)
                presenter.getInquiryTabungan(InquiryOpenAccountRequest(mProduct.productType))
            }
            Constant.OPEN_ACCOUNT_JUNIO ->{
                mUrlInquiry = GeneralHelper.getString(R.string.url_buka_tabungan_junio_inquiry_revamp)
                murlKonfirmasi = GeneralHelper.getString(R.string.url_buka_tabungan_junio_konfirmasi_revamp)
                mUrlPayment = GeneralHelper.getString(R.string.url_buka_tabungan_junio_payment_revamp)
                presenter.setUrlInquiry(mUrlInquiry)
                presenter.getInquiryTabunganJunio(InquiryOpenAccountRequest(
                    mProduct.productType))

            }
            Constant.OPEN_ACCOUNT_RENCANA ->  {
                HitungRencanaActivity.launchIntent(
                    this,
                    false,
                    mProduct.productType
                )
            }
            Constant.OPEN_ACCOUNT_VALAS ->  {
                mUrlInquiry = GeneralHelper.getString(R.string.url_buka_tabungan_valas_form)
                presenter.setUrlFormValas(mUrlInquiry)
                presenter.getFormTabunganValas(InquiryOpenAccountRequest(mProduct.productType))
            }
            else -> {
            }
        }
    }

    override fun onType(product: JenisTabunganResponse.Product, position: Int) {
        mProduct = product
        mPosition = position
    }

    override fun onDestroy() {
        presenter.stop()
        super.onDestroy()
    }

    override fun onClickYes() {
        super.onBackPressed()
        finish()
    }

    override fun onClickToSafety() {
        presenter.getPusatBantuanSafety(Constant.ID_PUSAT_BANTUAN_SAFETY_MODE)
    }

    fun setParameterModel(): ParameterPilihKantorModel? {
        val parameterModel = ParameterPilihKantorModel()
        parameterModel.type = mProduct.type
        parameterModel.productType = mProduct.productType
        parameterModel.urlOwnLocation = urlLokasiSendiri
        parameterModel.urlSearchLocation = urlLokasiPencarian
        parameterModel.urlInquiry = mUrlInquiry
        parameterModel.name = mProduct.name
        return parameterModel
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_BUKA_REKENING && data != null) {
            if (resultCode == RESULT_OK ) {
                setResult(RESULT_OK, data)
                finish()
            } else if (resultCode == RESULT_CANCELED) {
                setResult(RESULT_CANCELED, data)
                finish()
            }else if (resultCode == RESULT_FIRST_USER){
                this.setResult(RESULT_FIRST_USER, data)
                finish()
            }

        }
    }

    override fun onSuccessJunioInquiry(inquiryGeneralOpenAccountResponse: InquiryOpenAccJunioResponse) {
        FormBritamaJunioRevActivity.launchIntent(
            this,
            mProduct, inquiryGeneralOpenAccountResponse,murlKonfirmasi,mUrlPayment)
    }

    override fun onClickDialogWorkingHourse() {
        dialogWorkingHours!!.dismiss()
    }

    fun setButton(boolean: Boolean){
        if (boolean){
            btnRekeningBaru.isEnabled = true
            btnRekeningBaru.setTextColor(GeneralHelper.getColor(R.color.whiteColor))
            btnRekeningBaru.background = ContextCompat.getDrawable(this, R.drawable.button_primary_bg)
        }else{
            btnRekeningBaru.isEnabled = false
            btnRekeningBaru.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
            btnRekeningBaru.background = ContextCompat.getDrawable(this, R.drawable.rounded_button_disabled_revamp)
        }
    }

}