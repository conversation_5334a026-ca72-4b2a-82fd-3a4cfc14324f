@file:JvmName("ViewExt")
package id.co.bri.brimo.util.extension

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import id.co.bri.brimo.util.OnSingleClickListener

fun View.visible() {
    this.visibility = View.VISIBLE
}

fun View.invisible() {
    visibility = View.INVISIBLE
}

fun View.gone() {
    this.visibility = View.GONE
}

fun View.visibleView(isVisible: Boolean) {
    this.visibility = if (isVisible) View.VISIBLE else View.GONE
}

fun View.makeEnable() {
    this.isEnabled = true
}

fun View.makeDisable() {
    this.isEnabled = false
}

fun ViewGroup.layInflater() = LayoutInflater.from(this.context)

fun View.setOnSingleClickListener(l: (View) -> Unit) {
    setOnClickListener(OnSingleClickListener(l))
}
