package id.co.bri.brimo.ui.activities.dompetdigitalreskin

import id.co.bri.brimo.util.custom_numpad.CustomNumpadHelper
import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Rect
import android.graphics.Typeface
import android.os.Bundle
import android.provider.ContactsContract
import android.text.Editable
import android.text.InputType
import android.text.Spannable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.widget.EditText
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.HistoryDompetDigitalAdapterNs
import id.co.bri.brimo.adapters.ListEwalletAdapter
import id.co.bri.brimo.adapters.SavedAdapterNs
import id.co.bri.brimo.adapters.dompetdigital.ConnectedWalletsGridAdapter
import id.co.bri.brimo.contract.IPresenter.dompetdigitalreskin.IFormDompetDigitalReskinPresenter
import id.co.bri.brimo.contract.IView.dompetdigitalreskin.IFormDompetDigitalReskinView
import id.co.bri.brimo.databinding.ActivityFormDompetDigitalReskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimo.models.apimodel.response.EwalletBalanceListResponse
import id.co.bri.brimo.models.apimodel.response.EwalletBalanceResponse
import id.co.bri.brimo.models.apimodel.response.EwalletProductListResponse
import id.co.bri.brimo.models.apimodel.response.EwalletProductResponse
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.HistoryResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.DompetDigitalResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.EwalletListItem
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse
import id.co.bri.brimo.ui.activities.FormEditSavedReskinActivity
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.fragments.UpdateSavedItemNsFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.WalletConnectionBottomSheet
import id.co.bri.brimo.ui.fragments.bottomsheet.WalletSelectionBottomSheet
import id.co.bri.brimo.util.custom_numpad.NumpadType
import javax.inject.Inject

class FormDompetDigitalReskinActivity : NewSkinBaseActivity(),
    IFormDompetDigitalReskinView,
    SavedAdapterNs.ClickItem,
    HistoryDompetDigitalAdapterNs.ClickItem,
    UpdateSavedItemNsFragment.UpdateSavedInterface,
    ConnectedWalletsGridAdapter.OnWalletClickListener,
    ListEwalletAdapter.onClickDompetItem {

    private lateinit var binding: ActivityFormDompetDigitalReskinBinding
    private val TAG: String = "FormDompetDigitalReskinActivity"
    private var mNominal = ""
    private var mPhoneNumber = ""
    private var isFromBindingHome = false
    private var selectedConnectedWallet: EwalletProductResponse? = null

    @Inject
    lateinit var presenter: IFormDompetDigitalReskinPresenter<IFormDompetDigitalReskinView>

    // Adapters
    private lateinit var savedAdapter: SavedAdapterNs
    private lateinit var historyAdapter: HistoryDompetDigitalAdapterNs
    private lateinit var connectedWalletsAdapter: ConnectedWalletsGridAdapter

    // Data lists (used for business logic and empty state management)
    private var savedResponses: ArrayList<SavedResponse> = ArrayList()
    private var historyResponses: ArrayList<HistoryResponse> = ArrayList()
    private var connectedWallets: ArrayList<EwalletProductResponse> = ArrayList()
    private var ewalletListItems: List<EwalletListItem> = ArrayList()
    private var ewalletBalanceResponses: ArrayList<EwalletBalanceResponse> = ArrayList()

    // Adapter data (separate copies to avoid reference sharing issues with adapter's internal lists)
    private var connectedWalletsInfo: ArrayList<EwalletProductResponse> = ArrayList()
    private var ewalletBalanceInfo: ArrayList<EwalletBalanceResponse> = ArrayList()

    // E-wallet data
    private var mEwalletProductListModel: EwalletProductListResponse? = null
    private var mCellPhoneNumber: String? = null

    private var errorMessage: String? = null

    // Selected wallet
    private var selectedWallet: EwalletListItem? = null

    // Custom hint
    private lateinit var customHint: SpannableString

    override fun findMyCustomKeypadView(): View = binding.keypad

//    private lateinit var numpadHelper: CustomNumpadHelper

    // Current tab
    private var currentTab = TAB_TERHUBUNG
    private lateinit var skeleton: SkeletonScreen
    private var skeletonBindingList: SkeletonScreen? = null


    companion object {
        const val TAG = "FormDompetDigitalReskinActivity"
        const val TAB_TERHUBUNG = 0
        const val TAB_FAVORIT = 1
        const val TAB_RIWAYAT = 2

        @JvmStatic
        @JvmOverloads
        fun launchIntent(
            caller: Activity,
            fromFastMenu: Boolean,
            fromBindingHome: Boolean = false
        ) {
            val intent = Intent(caller, FormDompetDigitalReskinActivity::class.java)
            intent.putExtra(Constant.TAG_FROM_FASTMENU, fromFastMenu)
            intent.putExtra(Constant.TAG_FROM_BINDING_HOME, fromBindingHome)
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFormDompetDigitalReskinBinding.inflate(layoutInflater)
        skeleton = Skeleton.bind(binding.llContent)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.skeleton_form_dompet_digital_reskin)
            .show()

        setContentView(binding.root)

        if (intent.extras != null) {
            parseDataIntent(intent.extras)
        }

        customHint = customPrefixHint(
            "",
            GeneralHelper.getString(R.string.hint_label_no_hp),
            false
        )

        injectDependency()
        setupView()
        setupAdapters()
        setupTabFunctionality()
        buttonClick()
        setupChangeListeners()

    }

//    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
//        if (ev?.action == MotionEvent.ACTION_DOWN) {
//            // Check if custom numpad is visible and handle dismissal
//            if (::numpadHelper.isInitialized && numpadHelper.isKeyboardShowing()) {
//                val tappedInsideNumpad = numpadHelper.isTouchInsideNumpad(ev)
//
//                Log.d("FormDompetDigital", "Touch event - Inside numpad: $tappedInsideNumpad")
//
//                // If tapped inside numpad, prevent BaseActivity from interfering
//                if (tappedInsideNumpad) {
//                    Log.d("FormDompetDigital", "Touch inside numpad - preventing dismissal")
//                    // Tell numpad helper to ignore next focus change and ensure cursor stays visible
//                    numpadHelper.setIgnoreNextFocusChange(true)
//                    // Ensure cursor remains visible after touch handling
//                    numpadHelper.ensureCursorVisible()
//
//                    // Also directly ensure cursor on the BaseInputView's EditText
//                    ensureCursorVisibleOnPhoneNumber()
//
//                    return super.dispatchTouchEvent(ev)
//                }
//
//                // Only dismiss if tapped outside the numpad
//                val view = currentFocus
//
//                // Check if tapped outside EditText as well
//                val tappedOutsideEditText = if (view is EditText) {
//                    val outRect = Rect()
//                    view.getGlobalVisibleRect(outRect)
//                    !outRect.contains(ev.rawX.toInt(), ev.rawY.toInt())
//                } else {
//                    true // No EditText focused, consider as outside
//                }
//
//                Log.d("FormDompetDigital", "Outside EditText: $tappedOutsideEditText")
//
//                // Only hide keyboard if tapped outside both EditText and numpad
//                if (tappedOutsideEditText) {
//                    Log.d("FormDompetDigital", "Hiding keyboard")
//                    view?.clearFocus()
//                    numpadHelper.hideKeyboard()
//                }
//            }
//        }
//        return super.dispatchTouchEvent(ev)
//    }
//
//    private fun ensureCursorVisibleOnPhoneNumber() {
//        // Access the internal EditText of BaseInputView directly
//        try {
//            val baseInputEditText = binding.etPhoneNumber.getEditText()
//            val numpadEditText = numpadHelper.getEditText()
//
//            Log.d("FormDompetDigital", "BaseInputView EditText: ${baseInputEditText.javaClass.simpleName}, hasFocus: ${baseInputEditText.hasFocus()}, isCursorVisible: ${baseInputEditText.isCursorVisible}")
//            Log.d("FormDompetDigital", "Numpad EditText: ${numpadEditText.javaClass.simpleName}, hasFocus: ${numpadEditText.hasFocus()}, isCursorVisible: ${numpadEditText.isCursorVisible}")
//            Log.d("FormDompetDigital", "Are they the same object? ${baseInputEditText === numpadEditText}")
//
//            // Ensure cursor on BaseInputView's EditText
//            binding.etPhoneNumber.ensureCursorVisible()
//
//            // Also ensure on numpad's EditText (should be the same)
//            numpadHelper.ensureCursorVisible()
//
//            Log.d("FormDompetDigital", "After ensuring - BaseInput hasFocus: ${baseInputEditText.hasFocus()}, isCursorVisible: ${baseInputEditText.isCursorVisible}")
//        } catch (e: Exception) {
//            Log.e("FormDompetDigital", "Error ensuring cursor visibility", e)
//        }
//    }

    private fun parseDataIntent(extras: Bundle?) {
        if (extras != null) {
            isFromFastMenu = extras.getBoolean(Constant.TAG_FROM_FASTMENU, false)
            isFromBindingHome = extras.getBoolean(Constant.TAG_FROM_BINDING_HOME, false)
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this

        if (isFromFastMenu) {
            presenter.setUrlForm(GeneralHelper.getString(R.string.url_fm_form_dompet_digital_v3))
            presenter.setUrlInquiry(GeneralHelper.getString(R.string.url_fm_inquiry_dompet_digital_v5))
            presenter.setUrlConfirm(GeneralHelper.getString(R.string.url_fm_confirmation_dompet_digital_v3))
            presenter.setUrlPayment(GeneralHelper.getString(R.string.url_fm_payment_dompet_digital_v3))
            presenter.getDataFormFastMenu()
        } else {
            presenter.setUrlForm(GeneralHelper.getString(R.string.url_form_dompet_digital_v3))
            presenter.setUrlInquiry(GeneralHelper.getString(R.string.url_inquiry_dompet_digital_v5))
            presenter.setUrlConfirm(GeneralHelper.getString(R.string.url_confirmation_dompet_digital_v3))
            presenter.setUrlPayment(GeneralHelper.getString(R.string.url_payment_dompet_digital_v3))
            presenter.setUrlEwalletBindingList(GeneralHelper.getString(R.string.url_ewallet_binding_list))
            presenter.setUrlEwalletBalance(GeneralHelper.getString(R.string.url_ewallet_get_balance))
            presenter.getDataForm()
        }
        presenter.setIsFromFastMenu(isFromFastMenu)
        presenter.start()
    }

    private fun setupView() {
        if (isFromFastMenu) {
            binding.llContentMain.visibility = View.GONE
            binding.btnContainer.visibility = View.GONE
            binding.tabTerhubung.visibility = View.GONE
            binding.llConnectedWallets.visibility = View.GONE
            binding.llNoConnectedWallets.visibility = View.GONE
        }

        binding.etPhoneNumber.apply {
            setInputType(InputType.TYPE_CLASS_NUMBER)
//            attachNumpad(this@FormDompetDigitalReskinActivity, NumpadType.PHONE, { pin ->
//            }, onFocusChanged = { hasFocus ->
//                updateHintBasedOnState()
//            }, onAttached = { numpad ->
//                numpadHelper = numpad
//            })
            isEnabled = false
            addEndIcon(R.drawable.ic_contact_disable_reskin) {}

            setHint(customHint)

            setOnInputFocusChangeListener { _, hasFocus ->
                updateHintBasedOnState()
            }
        }

        GeneralHelperNewSkin.setToolbar(
            this,
            binding.toolbar.toolbar,
            getTitleBar()
        )
        setupKeypad()
        setTextForm()
    }

    private fun setupKeypad() = with(binding) {
        keypad.attachToEditText(binding.etPhoneNumber.getEditText())
    }

    private fun updateHintBasedOnState() {
        val isExpanded =
            binding.etPhoneNumber.hasFocus() || binding.etPhoneNumber.getText().isNotEmpty()
        if (isExpanded) {
            val expandedHint = SpannableString(getString(R.string.hint_label_no_hp))
            expandedHint.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(this, R.color.text_black_default_ns)),
                0,
                expandedHint.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            binding.etPhoneNumber.setHint(expandedHint)
            binding.etPhoneNumber.setPrefixTextBold(true)
            binding.etPhoneNumber.setPrefixTextColor(R.color.text_disabled_default_ns)
        } else {
            binding.etPhoneNumber.setHint(customHint)
        }
    }

    protected fun customPrefixHint(
        prefix: String,
        label: String,
        isEnabled: Boolean
    ): SpannableString {
        val spannable = SpannableString("$prefix  $label")
        spannable.setSpan(
            StyleSpan(Typeface.BOLD),
            0,
            prefix.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannable.setSpan(
            ForegroundColorSpan(ContextCompat.getColor(this, R.color.text_disabled_default_ns)),
            0,
            prefix.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannable.setSpan(
            ForegroundColorSpan(
                ContextCompat.getColor(
                    this,
                    if (!isEnabled) R.color.text_disabled_default_ns else R.color.text_black_default_ns
                )
            ),
            prefix.length,
            spannable.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        return spannable
    }


    override fun changeText(charSequence: CharSequence?, i: Int, i1: Int, i2: Int) {
        validatePhoneNumberAndUpdateButton()
    }

    private fun validatePhoneNumberAndUpdateButton() {
        val minLength = 9
        val maxLength = 12

        val inputLength = binding.etPhoneNumber.getText().length

        // Handle phone number validation errors
        if (inputLength > 0) { // Only show errors if user has started typing
            if (inputLength < minLength) {
                binding.etPhoneNumber.setError(
                    String.format(
                        GeneralHelper.getString(R.string.phone_minimal_input),
                        (minLength + 1).toString()
                    )
                )
            } else if (inputLength > maxLength) {
                binding.etPhoneNumber.setError(
                    String.format(
                        GeneralHelper.getString(R.string.phone_maksimal_input),
                        (maxLength + 1).toString()
                    )
                )
            } else {
                binding.etPhoneNumber.clearError()
            }
        } else {
            binding.etPhoneNumber.clearError()
        }

        // Update button state based on both wallet selection and phone number validity
        updateSubmitButtonState()
    }

    private fun updateSubmitButtonState() {
        val minLength = 9
        val maxLength = 12
        val inputLength = binding.etPhoneNumber.getText().length

        val isWalletSelected = selectedWallet != null
        val isPhoneNumberValid = inputLength in minLength..maxLength

        val isFormValid = isWalletSelected && isPhoneNumberValid

        binding.btnSubmit.isEnabled = isFormValid
    }

    protected fun checkContactPermission() {
        if (ContextCompat.checkSelfPermission(
                this, Manifest.permission.READ_CONTACTS
            ) == PackageManager.PERMISSION_DENIED
        ) {
            ActivityCompat.requestPermissions(
                this, arrayOf(Manifest.permission.READ_CONTACTS), Constant.REQ_READ_CONTACT
            )
        } else {
            pickContact()
        }
    }

    private fun pickContact() {
        val intent = Intent(Intent.ACTION_PICK, ContactsContract.Contacts.CONTENT_URI)
        intent.type = ContactsContract.CommonDataKinds.Phone.CONTENT_TYPE
        startActivityForResult(intent, Constant.REQ_READ_CONTACT)
    }

    private fun setupAdapters() {
        // Connected wallets adapter with GridLayoutManager
        val gridLayoutManager = GridLayoutManager(this, 2)
        binding.rvConnectedWallets.layoutManager = gridLayoutManager
        connectedWalletsAdapter =
            ConnectedWalletsGridAdapter(this, connectedWalletsInfo, ewalletBalanceInfo, this)
        binding.rvConnectedWallets.adapter = connectedWalletsAdapter

        // Saved adapter
        initiateSavedAdapter()

        // History adapter
        initiateHistoryAdapter()
    }

    private fun setupTabFunctionality() {
        // Default to Terhubung tab
        if (isFromFastMenu) switchToFavoritTab() else switchToTerhubungTab()
    }

    private fun setupChangeListeners() {
        // Add text change listener for phone number
        binding.etPhoneNumber.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                changeText(s, start, before, count)
            }

            override fun afterTextChanged(s: Editable?) {
                updateHintBasedOnState()
                binding.etPhoneNumber.removeAllEndIcons()
                if (s != null && s.isNotEmpty()) {
                    binding.etPhoneNumber.addEndIcon(
                        R.drawable.ic_clear_ns,
                        sizeDp = 24,
                        marginDp = 5
                    ) {
                        binding.etPhoneNumber.clearText()
                    }
                }
                binding.etPhoneNumber.addEndIcon(R.drawable.ic_contact_reskin) {
                    checkContactPermission()
                }
            }
        })
    }

    private fun switchToTerhubungTab() {
        currentTab = TAB_TERHUBUNG
        updateTabAppearance()
        showTerhubungContent()
    }

    private fun switchToFavoritTab() {
        currentTab = TAB_FAVORIT
        updateTabAppearance()
        showFavoritContent()
    }

    private fun switchToRiwayatTab() {
        currentTab = TAB_RIWAYAT
        updateTabAppearance()
        showRiwayatContent()
    }

    private fun updateTabAppearance() {
        // Reset all tabs
        binding.tabTerhubung.setTextColor(ContextCompat.getColor(this, R.color.black))
        binding.tabTerhubung.setBackgroundResource(R.drawable.rounded_button_neutral_ns)

        binding.tabFavorit.setTextColor(ContextCompat.getColor(this, R.color.black))
        binding.tabFavorit.setBackgroundResource(R.drawable.rounded_button_neutral_ns)

        binding.tabRiwayat.setTextColor(ContextCompat.getColor(this, R.color.black))
        binding.tabRiwayat.setBackgroundResource(R.drawable.rounded_button_neutral_ns)

        // Set active tab
        when (currentTab) {
            TAB_TERHUBUNG -> {
                binding.tabTerhubung.setTextColor(
                    ContextCompat.getColor(
                        this,
                        R.color.text_brand_primary_ns
                    )
                )
                binding.tabTerhubung.setBackgroundResource(R.drawable.rounded_button_soft_ns)
            }

            TAB_FAVORIT -> {
                binding.tabFavorit.setTextColor(
                    ContextCompat.getColor(
                        this,
                        R.color.text_brand_primary_ns
                    )
                )
                binding.tabFavorit.setBackgroundResource(R.drawable.rounded_button_soft_ns)
            }

            TAB_RIWAYAT -> {
                binding.tabRiwayat.setTextColor(
                    ContextCompat.getColor(
                        this,
                        R.color.text_brand_primary_ns
                    )
                )
                binding.tabRiwayat.setBackgroundResource(R.drawable.rounded_button_soft_ns)
            }
        }
    }

    private fun showTerhubungContent() {
        binding.contentTerhubung.visibility = View.VISIBLE
        binding.contentFavorit.visibility = View.GONE
        binding.contentRiwayat.visibility = View.GONE
    }

    private fun showFavoritContent() {
        binding.contentTerhubung.visibility = View.GONE
        binding.contentFavorit.visibility = View.VISIBLE
        binding.contentRiwayat.visibility = View.GONE
    }

    private fun showRiwayatContent() {
        binding.contentTerhubung.visibility = View.GONE
        binding.contentFavorit.visibility = View.GONE
        binding.contentRiwayat.visibility = View.VISIBLE
    }


    private fun showWalletSelectionBottomSheet() {
        if (ewalletListItems.isNotEmpty()) {
            showWalletSelectionBottomSheetImpl(ewalletListItems)
        } else {
            // Show message that wallet list is not loaded yet
            showSnackbarErrorMessage(
                GeneralHelper.getString(R.string.txt_wallet_loading),
                ALERT_ERROR,
                this,
                false
            )
        }
    }

    override fun setTextForm() {
        binding.btnSubmit.text = GeneralHelper.getString(R.string.lanjutkan)

    }

    override fun getTitleBar(): String {
        return GeneralHelper.getString(R.string.ewallet)
    }

    override fun getDefaultIconResource(): Int {
        return 0
    }

    // IFormDompetDigitalReskinView implementations
    override fun onSuccessGetForm(dompetDigitalResponse: DompetDigitalResponse) {
        if (isFromFastMenu) hideSkeleton(true)

        ewalletListItems = dompetDigitalResponse.ewalletListItems ?: emptyList()
        savedResponses.clear()
        savedResponses.addAll(dompetDigitalResponse.saved ?: emptyList())
        historyResponses.clear()
        historyResponses.addAll(dompetDigitalResponse.history ?: emptyList())

        savedAdapter.notifyDataSetChanged()
        historyAdapter.notifyDataSetChanged()

        updateEmptyStates()
    }

    override fun onSuccessGetInquiry(
        inquiryDompetDigitalResponse: InquiryDompetDigitalResponse,
        urlConfirm: String,
        urlPayment: String,
        isFromFastMenu: Boolean
    ) {
        // Check if this inquiry is from connected wallet click or regular form submission
        val connectedWallet = selectedConnectedWallet
        if (connectedWallet != null) {
            // Find balance for the selected connected wallet
            val walletBalance = ewalletBalanceInfo.find {
                it.type.equals(connectedWallet.type, ignoreCase = true)
            }

            val cellPhoneNumber = mCellPhoneNumber
            if (cellPhoneNumber != null) {
                InquiryDompetDigitalReskinActivity.launchIntent(
                    this,
                    inquiryDompetDigitalResponse,
                    urlConfirm,
                    urlPayment,
                    false,
                    mNominal,
                    cellPhoneNumber,
                    connectedWallet,
                    walletBalance
                )

                // Reset selected connected wallet
                selectedConnectedWallet = null
            } else {
                // Handle case where cell phone number is null
                showSnackbarErrorMessage(
                    GeneralHelper.getString(R.string.txt_no_not_found),
                    ALERT_ERROR,
                    this,
                    false
                )
            }
        } else {
            // Regular form submission
            InquiryDompetDigitalReskinActivity.launchIntent(
                this,
                inquiryDompetDigitalResponse,
                urlConfirm,
                urlPayment,
                false,
                mNominal,
                mPhoneNumber,
            )
        }
    }

    override fun onSuccessGetInquirySaved(
        inquiryDompetDigitalResponse: InquiryDompetDigitalResponse,
        urlConfirm: String,
        urlPayment: String,
        isFromFastMenu: Boolean,
        purchaseNumber: String
    ) {
        if (isFromFastMenu) {
            InquiryDompetDigitalReskinActivity.launchIntent(
                this,
                inquiryDompetDigitalResponse,
                urlConfirm,
                urlPayment,
                true,
                mNominal,
                purchaseNumber,
            )
        } else {
            InquiryDompetDigitalReskinActivity.launchIntent(
                this,
                inquiryDompetDigitalResponse,
                urlConfirm,
                urlPayment,
                false,
                mNominal,
                purchaseNumber,
            )
        }
    }

    override fun onSuccessGetEwalletBinding(ewalletProductListResponse: EwalletProductListResponse) {

        // Store the data for wallet connection bottom sheet
        mEwalletProductListModel = ewalletProductListResponse
        mCellPhoneNumber = ewalletProductListResponse.cellphoneNumber

        // Update connected wallets list with only bound wallets
        connectedWallets.clear()
        ewalletProductListResponse.products?.forEach { product ->
            if (product.isBind == 1) { // Only connected wallets
                connectedWallets.add(product)
            }
        }

        // Update adapter data variables
        connectedWalletsInfo.clear()
        connectedWalletsInfo.addAll(connectedWallets)

        connectedWalletsAdapter.updateWallets(ArrayList(connectedWalletsInfo))
        connectedWalletsAdapter.notifyDataSetChanged()
        if (isFromBindingHome) showWalletConnectionBottomSheet()
        updateEmptyStates()
    }

    override fun onSuccessGetEwalletList(
        ewalletProductListResponse: EwalletProductListResponse,
        ewalletBalanceListResponse: EwalletBalanceListResponse
    ) {
        // Store the data
        mEwalletProductListModel = ewalletProductListResponse
        mCellPhoneNumber = ewalletProductListResponse.cellphoneNumber

        // Update connected wallets list with only bound wallets
        connectedWallets.clear()
        ewalletProductListResponse.products?.forEach { product ->
            if (product.isBind == 1) { // Only connected wallets
                connectedWallets.add(product)
            }
        }

        // Update balance data
        ewalletBalanceResponses.clear()
        ewalletBalanceListResponse.balances?.let { balances ->
            ewalletBalanceResponses.addAll(balances)
        }

        // Update adapter data variables
        connectedWalletsInfo.clear()
        connectedWalletsInfo.addAll(connectedWallets)
        ewalletBalanceInfo.clear()
        ewalletBalanceInfo.addAll(ewalletBalanceResponses)

        // Update the adapter with balance data
        connectedWalletsAdapter.updateWallets(ArrayList(connectedWalletsInfo))
        connectedWalletsAdapter.updateBalances(ArrayList(ewalletBalanceInfo))
        connectedWalletsAdapter.notifyDataSetChanged()
        updateEmptyStates()
    }

    override fun onSuccessGetEwalletList(ewalletProductListResponse: EwalletProductListResponse) {
        // Store the data
        mEwalletProductListModel = ewalletProductListResponse
        mCellPhoneNumber = ewalletProductListResponse.cellphoneNumber

        // Update connected wallets list with only bound wallets
        connectedWallets.clear()
        ewalletProductListResponse.products?.forEach { product ->
            if (product.isBind == 1) { // Only connected wallets
                connectedWallets.add(product)
            }
        }

        // Update adapter data variables
        connectedWalletsInfo.clear()
        connectedWalletsInfo.addAll(connectedWallets)

        // Update the adapter without balance data
        connectedWalletsAdapter.updateWallets(ArrayList(connectedWalletsInfo))
        connectedWalletsAdapter.updateBalances(ArrayList())
        connectedWalletsAdapter.notifyDataSetChanged()
        updateEmptyStates()
    }

    override fun onFailedGetEwalletBinding(message: String) {
        updateEmptyStates()
    }

    //     ListEwalletAdapter.onClickDompetItem implementation
    override fun onActionClick(isEditable: Boolean, ewalletProductModel: EwalletProductResponse) {
        // Handle connected wallet click

        if (ewalletProductModel.isBind == 1) {
            // Wallet is connected, use it for transaction
            binding.etWallet.setText(ewalletProductModel.title)
            // Set selected wallet based on connected wallet
            selectedWallet = EwalletListItem().apply {
                code = ewalletProductModel.ewallet_code
                name = ewalletProductModel.title
                iconName = ewalletProductModel.iconName
                iconPath = ewalletProductModel.iconPath
                extra = ewalletProductModel.code
            }

            // Load wallet icon
            GeneralHelper.loadIconTransaction(
                this@FormDompetDigitalReskinActivity,
                ewalletProductModel.iconPath,
                ewalletProductModel.iconName?.split("\\.")?.get(0) ?: "",
                binding.ivWalletIcon,
                0
            )

            binding.iconContainer.visibility = View.VISIBLE
            binding.regionTextview.visibility = View.VISIBLE
            binding.regionTextview.setText(GeneralHelper.getString(R.string.jenis_e_wallet))

            // Update button state when connected wallet is selected
            updateSubmitButtonState()
        }
    }

    private fun showWalletSelectionBottomSheetImpl(ewalletListItems: List<EwalletListItem>) {

        val bottomSheet = WalletSelectionBottomSheet(ewalletListItems) { selectedWallet ->

            onWalletSelected(selectedWallet)
        }
        bottomSheet.show(supportFragmentManager, TAG)
    }

    private fun showWalletConnectionBottomSheet() {

        mEwalletProductListModel?.products?.let { products ->
            // Show all wallets (both bound and unbound)
            if (products.isNotEmpty()) {
                val bottomSheet = WalletConnectionBottomSheet(products) { selectedWallet ->
                    // Only allow navigation for unbound wallets
                    if (selectedWallet.isBind != 1) {
                        // Navigate to TNC activity
                        TncDompetDigitalReskinActivity.launchIntent(
                            this,
                            selectedWallet,
                            mCellPhoneNumber ?: "",
                            Constant.REQ_WALLET_BINDING_SUCCESS
                        )
                    }
                }
                bottomSheet.show(supportFragmentManager, TAG)
            } else {
                showSnackbarErrorMessage(
                    GeneralHelper.getString(R.string.txt_wallet_not_found),
                    ALERT_ERROR,
                    this,
                    false
                )
            }
        } ?: run {
            showSnackbarErrorMessage(
                GeneralHelper.getString(R.string.txt_wallet_loading),
                ALERT_ERROR,
                this,
                false
            )
        }
    }

    override fun onWalletSelected(selectedWallet: EwalletListItem) {
        this.selectedWallet = selectedWallet
        binding.etWallet.setText(selectedWallet.name)
        binding.etWallet.setTypeface(binding.etWallet.getTypeface(), Typeface.BOLD);

        // Load wallet icon
        GeneralHelper.loadIconTransaction(
            this,
            selectedWallet.iconPath,
            selectedWallet.iconName?.split("\\.")?.get(0) ?: "",
            binding.ivWalletIcon,
            selectedWallet.defaultIcon
        )

        binding.iconContainer.visibility = View.VISIBLE
        binding.regionTextview.visibility = View.VISIBLE
        binding.regionTextview.setText(GeneralHelper.getString(R.string.jenis_e_wallet))
        binding.etPhoneNumber.apply {
            isEnabled = true
            removeAllEndIcons()
            if (getText().isNotEmpty()) {
                binding.etPhoneNumber.addEndIcon(
                    R.drawable.ic_clear_ns,
                    sizeDp = 24,
                    marginDp = 5
                ) {
                    binding.etPhoneNumber.clearText()
                }
            }
            addEndIcon(R.drawable.ic_contact_reskin) {
                checkContactPermission()
            }
            customHint = customPrefixHint(
                GeneralHelper.getString(R.string.hint_prefix_62),
                GeneralHelper.getString(R.string.hint_label_no_hp),
                true
            )
            setInputTypefaceStyle(Typeface.BOLD)
            setHint(customHint)
            // Update hint based on current state to ensure correct color
            updateHintBasedOnState()
        }

        // Update button state when wallet is selected
        updateSubmitButtonState()
    }

    private fun updateEmptyStates() {
        // Update Terhubung tab empty state
        if (connectedWallets.isEmpty()) {
            binding.llConnectedWallets.visibility = View.GONE
            binding.llNoConnectedWallets.visibility = View.VISIBLE
        } else {
            binding.llConnectedWallets.visibility = View.VISIBLE
            binding.llNoConnectedWallets.visibility = View.GONE
        }

        // Update Favorit tab empty state
        if (savedResponses.isEmpty()) {
            binding.rvDaftarFavorit.visibility = View.GONE
            binding.llNoDataSaved.visibility = View.VISIBLE
        } else {
            binding.rvDaftarFavorit.visibility = View.VISIBLE
            binding.llNoDataSaved.visibility = View.GONE
        }

        // Update Riwayat tab empty state
        if (historyResponses.isEmpty()) {
            binding.rvRiwayat.visibility = View.GONE
            binding.llNoHistory.visibility = View.VISIBLE
        } else {
            binding.rvRiwayat.visibility = View.VISIBLE
            binding.llNoHistory.visibility = View.GONE
        }
    }

    // SavedAdapterNs.ClickItem implementations
    override fun onClickSavedItem(savedResponse: SavedResponse) {
        val s = savedResponse.value
        val strArr = s.split("\\|".toRegex()).dropLastWhile { it.isEmpty() }
            .toTypedArray()
        if (strArr.size > 2) {
            val ewalletCode = strArr[1]
            val corpCode = strArr[2]
            val purchaseNumber = strArr[3]
            mNominal = ""
            presenter.getDataInquirySaved(
                isFromFastMenu,
                ewalletCode,
                corpCode,
                purchaseNumber
            )
        }
    }

    override fun onClickUpdateItem(savedResponse: SavedResponse, position: Int) {
        val updateSavedItemFragment = UpdateSavedItemNsFragment(savedResponse, this, position)
        updateSavedItemFragment.show(supportFragmentManager, "")
    }

    // HistoryDompetDigitalAdapterNs.ClickItem implementations
    override fun onClickHistoryItem(historyResponse: HistoryResponse) {
        val s = historyResponse.value
        val strArr = s.split("\\|".toRegex()).dropLastWhile { it.isEmpty() }
            .toTypedArray()
        if (strArr.size > 1) {
            val ewalletCode = strArr[0]
            val corpCode = strArr[1]
            val purchaseNumber = strArr[2]
            mNominal = strArr[3]
            presenter.getDataInquirySaved(
                isFromFastMenu,
                ewalletCode,
                corpCode,
                purchaseNumber
            )
        }

    }

    // format phone number if the user did not input 0 in the beginning, add 0 in the beginning
    fun formatPhoneNumber(phoneNumber: String): String {
        val formattedNumber = if (phoneNumber.startsWith("0")) {
            phoneNumber
        } else {
            "0$phoneNumber"
        }
        return formattedNumber
    }

    override fun onException(message: String) {
        GeneralHelperNewSkin.triggerVibration(this, Constant.VIBRATE_ERROR)
        if (GeneralHelperNewSkin.isContains(Constant.LIST_TYPE_GAGAL_GANGGUAN_SISTEM, message)) {
            GeneralHelperNewSkin.showErrorBottomDialog(this, message)
        } else if (message.contains(Constant.Ewallet.TIME_OUT_ESB)) {
            OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
                fragmentManager = supportFragmentManager,
                imgDrawable = R.drawable.ic_sad_illustration,
                imgName = "ic_sad_illustration",
                titleTxt = GeneralHelper.getString(R.string.txt_error_dialog),
                subTitleTxt = GeneralHelper.getString(R.string.txt_error_dialog_desc),
                btnFirstFunction = {
                    selectedWallet?.let { wallet ->
                        mPhoneNumber = formatPhoneNumber(binding.etPhoneNumber.getText().trim())
                        if (mPhoneNumber.isNotEmpty()) {
                            presenter.getDataInquiry(
                                wallet.code ?: "",
                                wallet.extra ?: "",
                                mPhoneNumber
                            )
                        }
                    }
                },
                btnSecondFunction = {
                    // Close the dialog
                },
                isClickableOutside = true,
                firstBtnTxt = GeneralHelper.getString(R.string.retry),
                secondBtnTxt = GeneralHelper.getString(R.string.close),
                showCloseButton = true
            )
        } else if (GeneralHelperNewSkin.isContainsPartial(
                Constant.Ewallet.LIST_TYPE_NUMBER_NOT_FOUND,
                message
            )
        ) {
            binding.etPhoneNumber.setError(GeneralHelper.getString(R.string.txt_no_hp_tidak_ditemukan))
        } else {
            showSnackbarErrorMessage(message, ALERT_ERROR, this, false)
        }
    }

    // IBaseFormRevampView implementations
    override fun searchDataSaved() {
        // Do nothing
    }

    override fun initiateHistoryAdapter() {
        binding.rvRiwayat.layoutManager = LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        historyAdapter =
            HistoryDompetDigitalAdapterNs(
                this,
                historyResponses,
                this,
                getDefaultIconResource(),
                isFromFastMenu
            )
        binding.rvRiwayat.adapter = historyAdapter
    }

    override fun initiateSavedAdapter() {
        binding.rvDaftarFavorit.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        savedAdapter =
            SavedAdapterNs(this, savedResponses, this, getDefaultIconResource(), isFromFastMenu)
        binding.rvDaftarFavorit.adapter = savedAdapter

    }

    override fun hideSkeleton(hide: Boolean) {
        skeleton.hide()
        if (skeletonBindingList != null) {
            skeletonBindingList?.hide()
        }
    }

    override fun disableButtonSubmit(disable: Boolean) {
        binding.btnSubmit.isEnabled = !disable
    }

    override fun buttonClick() {
        binding.tabTerhubung.setOnClickListener { switchToTerhubungTab() }
        binding.tabFavorit.setOnClickListener { switchToFavoritTab() }
        binding.tabRiwayat.setOnClickListener { switchToRiwayatTab() }
        binding.etWallet.setOnClickListener {
            showWalletSelectionBottomSheet()
        }
        binding.llWalletSelection.setOnClickListener {
            showWalletSelectionBottomSheet()
        }
        binding.btnSubmit.setOnClickListener {
            selectedWallet?.let { wallet ->
                mPhoneNumber = formatPhoneNumber(binding.etPhoneNumber.getText().trim())
                if (mPhoneNumber.isNotEmpty()) {
                    presenter.getDataInquiry(
                        wallet.code ?: "",
                        wallet.extra ?: "",
                        mPhoneNumber
                    )
                }
            }
        }
        binding.searchviewBriva.setOnClickListener {
            SearchSavedHistoryDompetDigitalActivity.launchIntent(
                this,
                savedResponses,
                historyResponses,
                getDefaultIconResource(),
                isFromFastMenu
            )
        }
        binding.tvEditDompetDigital.setOnClickListener {
            EditBindingDompetDigitalActivity.launchIntent(this, connectedWallets, mCellPhoneNumber)
        }

        // Connect button click handler
        binding.btnConnect.setOnClickListener {
            showWalletConnectionBottomSheet()
        }
    }

    override fun onSuccessGetHistoryForm(historyResponses: List<HistoryResponse>) {
        this.historyResponses.clear()
        this.historyResponses.addAll(historyResponses)
        historyAdapter.notifyDataSetChanged()
        updateEmptyStates()
    }

    override fun onSuccessGetRestResponse(restResponse: RestResponse) {
        // Handle rest response
    }

    override fun onSuccessGetSavedForm(savedResponses: List<SavedResponse>) {
        this.savedResponses.clear()
        this.savedResponses.addAll(savedResponses)
        savedAdapter.notifyDataSetChanged()
        updateEmptyStates()
    }

    override fun onSuccessUpdate(savedResponse: SavedResponse, item: Int, type: Int) {
        // Handle update response
        val message = GeneralHelper.getString(R.array.type_option_desc_newskin, type)
        showSnackbarErrorMessage(message, ALERT_CONFIRM, this, false)

        // Refresh data without showing loading indicator since we just finished an operation
        if (presenter != null) {
            presenter.getDataFormSilent()
        }
        updateEmptyStates()
    }

    override fun checkDataHistorySavedList() {
        // Check if data is available
        updateEmptyStates()
    }

    override fun onHistorySavedEmpty() {
        // Handle empty state
        updateEmptyStates()
    }


    override fun onExceptionRevamp(message: String) {
        onException(message)
    }

    override fun onException06(response: ExceptionResponse) {
        // Handle exception 06
    }

    override fun onException99(message: String) {
        // Handle exception 99
    }

    override fun onExceptionFO(response: EmptyStateResponse) {
        // Handle FO exception
    }

    override fun onExceptionLimitExceed(response: GeneralResponse) {
        // Handle limit exceed exception
    }

    override fun onExceptionNoBackAction(message: String) {
        onException(message)
    }

    override fun onExceptionStatusNotMatch() {
        // Handle status not match exception
    }

    protected fun cekErrorMessage() {
        if (errorMessage != null) {
            showSnackbarErrorMessage(
                errorMessage, ALERT_ERROR,
                this, false
            )
            errorMessage = null
        }
    }

    protected open fun onContactPicked(number: String) {
        binding.etPhoneNumber.setText(number)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == Constant.REQ_READ_CONTACT && resultCode == RESULT_OK) {
            val contactUri = data?.data ?: return
            val projection = arrayOf(ContactsContract.CommonDataKinds.Phone.NUMBER)

            val cursor = contentResolver.query(contactUri, projection, null, null, null)
            cursor?.use {
                if (it.moveToFirst()) {
                    val column = it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER)
                    val number = it.getString(column).replace("[^\\d.]".toRegex(), "")
                    var newNumber: String
                    if (number.substring(0, 1) != "0") {
                        newNumber = number.substring(2)
                    } else {
                        newNumber = number.substring(1)
                    }
                    onContactPicked(newNumber)
                } else {
                    showSnackbarErrorMessage(
                        GeneralHelper.getString(R.string.contact_not_found),
                        ALERT_ERROR,
                        this,
                        false
                    )
                }
            }
        }

        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                finish()
            } else {
                this.setResult(RESULT_CANCELED, data)
                if (data != null) {
                    errorMessage =
                        data.getStringExtra(Constant.TAG_ERROR_MESSAGE)
                    cekErrorMessage()
                }
            }
        }

        if (requestCode == Constant.REQ_EDIT_SAVED && data != null) {
            if (resultCode == RESULT_OK) {
                handleEditOrSaveAction(Constant.EditOptionNs.EDIT)
            }
        }

        if (requestCode == Constant.REQ_SAVE_SAVED) {
            if (resultCode == RESULT_OK) {
                handleEditOrSaveAction(Constant.EditOptionNs.SAVE)
            } else if (resultCode == RESULT_CANCELED && data != null) {
                errorMessage =
                    data.getStringExtra(Constant.TAG_ERROR_MESSAGE)
                cekErrorMessage()
            }
        }

        if (requestCode == Constant.REQ_SEARCH_SAVED_HISTORY) {
            if (resultCode == RESULT_OK) {
                // Refetch data when returning from search activity
                if (presenter != null) {
                    if (isFromFastMenu) {
                        presenter.getDataFormFastMenu()
                    } else {
                        presenter.getDataForm()
                    }
                }
            }
        }

        if (requestCode == Constant.REQ_EDIT_BIND_WALLET || requestCode == Constant.REQ_WALLET_BINDING_SUCCESS) {
            skeletonBindingList = Skeleton.bind(binding.contentTerhubung).shimmer(true)
                .angle(20)
                .duration(1200)
                .load(R.layout.skeleton_binding_list_dompet_digital)
                .show()

            when (resultCode) {
                EditBindingDompetDigitalActivity.RESULT_CONNECT_NEW_WALLET -> {
                    // User clicked btnSubmit to connect new wallet, show wallet connection bottom sheet
                    skeletonBindingList?.hide()
                    showWalletConnectionBottomSheet()
                }

                EditBindingDompetDigitalActivity.RESULT_WALLET_CANCELED -> {
                    skeletonBindingList?.hide()
                }

                EditBindingDompetDigitalActivity.RESULT_WALLET_UNBOUND_LIST_EMPTY -> {
                    // User unbound wallet and list is now empty, refetch binding list
                    showSnackbarErrorMessage(
                        "E-wallet berhasil dihapus",
                        Constant.ALERT_CONFIRM,
                        this,
                        false
                    )
                    presenter.getEwalletBindingList()
                }

                EditBindingDompetDigitalActivity.RESULT_WALLET_UNBOUND_LIST_NOT_EMPTY -> {
                    // User unbound wallet but list is not empty, refetch binding list
                    presenter.getEwalletBindingList()
                }

                RESULT_OK -> {
                    // Fallback for any other RESULT_OK cases, refetch binding list
                    presenter.getEwalletBindingList()
                }

                else -> {
                    skeletonBindingList?.hide()
                }
            }
        }
    }

    override fun onUpdateItem(savedResponseItem: SavedResponse?, type: Int, position: Int) {
        val savedItem = savedResponseItem ?: return // Early return if null

        when (type) {
            Constant.EditOptionNs.FAV -> {
                presenter.setUpdateItem(
                    GeneralHelper.getString(R.string.url_wallet_favorite_saved_v5),
                    savedItem, position, type
                )
            }

            Constant.EditOptionNs.HAPUS -> {
                hapusConfirmation(savedItem, type, position)
            }

            Constant.EditOptionNs.EDIT -> {
                FormEditSavedReskinActivity.launchIntent(
                    this,
                    savedItem,
                    position,
                    getDefaultIconResource(),
                    GeneralHelper.getString(R.string.url_wallet_update_nickname_v3),
                    getString(R.string.jenis_e_wallet),
                    getString(R.string.txt_nomor_hp)
                )
            }

            Constant.EditOptionNs.NON_FAV -> {
                presenter.setUpdateItem(
                    GeneralHelper.getString(R.string.url_wallet_remove_saved_v3),
                    savedItem, position, Constant.EditOptionNs.NON_FAV
                )
            }
        }
    }

    private fun handleEditOrSaveAction(editOption: Int) {
        val message = GeneralHelper.getString(R.array.type_option_desc_newskin, editOption)
        showSnackbarErrorMessage(message, ALERT_CONFIRM, this, false)
        if (presenter != null) {
            presenter.getDataForm()
        }
    }

    private fun hapusConfirmation(savedResponseItem: SavedResponse, type: Int, position: Int) {
        showHapusConfirmationBottomSheet(savedResponseItem, type, position)
    }

    private fun showHapusConfirmationBottomSheet(
        savedResponseItem: SavedResponse,
        type: Int,
        position: Int
    ) {
        var spannableString: SpannableString

        savedResponseItem.let { item ->
            // Get the template string
            val template = GeneralHelper.getString(R.string.desc_hapus_dialog_ns)
            val fullText = String.format(template, item.title)

            spannableString = SpannableString(fullText)

            // Find the position of the title in the full text
            val titleStart = fullText.indexOf(item.title)
            val titleEnd = titleStart + item.title.length

            if (titleStart >= 0) {
                spannableString.setSpan(
                    StyleSpan(Typeface.BOLD),
                    titleStart,
                    titleEnd,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }

        OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmationWithStringFormat(
            fragmentManager = supportFragmentManager,
            imgDrawable = R.drawable.ic_sad_illustration,
            imgName = "ic_sad_illustration",
            titleTxt = GeneralHelper.getString(R.string.txt_hapus_favorit),
            subTitleTxt = spannableString,
            btnFirstFunction = {
                // Close the dialog
            },
            btnSecondFunction = {
                presenter.setUpdateItem(
                    GeneralHelper.getString(R.string.url_wallet_delete_saved_v3),
                    savedResponseItem, position, type
                )
            },
            isClickableOutside = true,
            withBgSecondBtn = false,
            firstBtnTxt = GeneralHelper.getString(R.string.batal),
            secondBtnTxt = GeneralHelper.getString(R.string.delete),
            showCloseButton = true,
            showPill = true
        )
    }

    // ConnectedWalletsGridAdapter.OnWalletClickListener implementation
    override fun onWalletClick(wallet: EwalletProductResponse) {
        wallet.ewallet_code?.let { ewalletCode ->
            val parts = ewalletCode.split("|")
            if (parts.size == 2) {
                val walletCode = parts[0]
                val corpCode = parts[1]
                val purchaseNumber = mCellPhoneNumber ?: ""

                if (purchaseNumber.isNotEmpty()) {
                    selectedConnectedWallet = wallet

                    presenter.getDataInquiry(walletCode, corpCode, purchaseNumber)
                } else {
                    showSnackbarErrorMessage(
                        GeneralHelper.getString(R.string.txt_no_not_found),
                        ALERT_ERROR,
                        this,
                        false
                    )
                }
            } else {
                showSnackbarErrorMessage(
                    GeneralHelper.getString(R.string.txt_wallet_code_not_valid),
                    ALERT_ERROR,
                    this,
                    false
                )
            }
        } ?: run {
            showSnackbarErrorMessage(
                GeneralHelper.getString(R.string.txt_wallet_code_not_available),
                ALERT_ERROR,
                this,
                false
            )
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        isFromFastMenu = false
    }

    override fun onConnectButtonClick() {
        showWalletConnectionBottomSheet()
    }
}