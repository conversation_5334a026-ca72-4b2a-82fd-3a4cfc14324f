package id.co.bri.brimo.ui.activities.dompetdigitalreskin

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.InputType
import android.text.Spannable
import android.text.SpannableString
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.GridLayoutManager
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dompetdigital.RekomendasiTopUpAdapterReskin
import id.co.bri.brimo.contract.IPresenter.dompetdigitalreskin.IInquiryDompetDigitalReskinPresenter
import id.co.bri.brimo.contract.IView.dompetdigitalreskin.IInquiryDompetDigitalReskinView
import id.co.bri.brimo.databinding.ActivityInquiryDompetDigitalReskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.BillingDetailOpen
import id.co.bri.brimo.models.apimodel.response.EwalletProductResponse
import id.co.bri.brimo.models.apimodel.response.EwalletBalanceResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.OptionAmountItem
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.customviews.switchbutton.SwitchView
import id.co.bri.brimo.ui.fragments.SumberDanaFragment
import id.co.bri.brimo.ui.fragments.SumberDanaFragmentRevamp
import id.co.bri.brimo.util.custom_numpad.CustomNumpadHelper
import id.co.bri.brimo.util.custom_numpad.NumpadType
import id.co.bri.brimo.util.attachNumpad
import java.math.BigInteger
import java.util.function.Consumer
import javax.inject.Inject

class InquiryDompetDigitalReskinActivity : NewSkinBaseActivity(), IInquiryDompetDigitalReskinView,
    SumberDanaFragmentRevamp.SelectSumberDanaInterface,
    SumberDanaFragment.SelectSumberDanaInterface, SwitchView.ISwitchListener {

    private lateinit var binding: ActivityInquiryDompetDigitalReskinBinding
    private lateinit var mbrivaOpenResponse: List<BillingDetailOpen>
    private lateinit var openModel: BillingDetailOpen
    private var mListFailed: List<Int>? = mutableListOf()
    private var mListAccountModel: List<AccountModel>? = null
    private var model: AccountModel? = null
    private var saldo: Double = 0.0
    private var counter: Int = 0
    private var minTrx: Long = 0
    private var maxTrx: Long = 0
    private var saveStr: String = ""
    private var minTrxString: String = ""
    private var maxTrxString: String = ""
    private var feeAdminString: String = ""
    private var nominalStrClr: String = ""
    private var nominalString: String = ""

    private lateinit var adapter: RekomendasiTopUpAdapterReskin
//    private lateinit var numpadHelper: CustomNumpadHelper
    private lateinit var unifiedTextWatcher: TextWatcher

    override fun findMyCustomKeypadView(): View = binding.keypad

    @Inject
    lateinit var presenter: IInquiryDompetDigitalReskinPresenter<IInquiryDompetDigitalReskinView>

    companion object {

        private lateinit var mInquiryDompetRevampResponse: InquiryDompetDigitalResponse
        private var mUrlConfirm: String = ""
        private var mUrlPayment: String = ""
        private var mNominal = ""
        private var mPhoneNumber = ""
        private var isFromFastMenu: Boolean = false
        private var mSelectedWallet: EwalletProductResponse? = null
        private var mWalletBalance: EwalletBalanceResponse? = null

        private const val TAG_RESPONSE = "response"
        private const val TAG_PHONE_NUMBER = "tag_phone_number"
        private const val TAG_WALLET_INFO = "wallet_info"
        private const val TAG_WALLET_BALANCE = "wallet_balance"
        private const val NOTE_CHARACTER_LIMIT = 30
        private const val NOMINAL_CHARACTER_LIMIT = 15

        @JvmStatic
        @JvmOverloads
        fun launchIntent(
            caller: Activity,
            inquiryDompetDigitalResponse: InquiryDompetDigitalResponse,
            urlConfirm: String,
            urlPayment: String,
            fromFastMenu: Boolean,
            nominal: String,
            phoneNumber: String,
            selectedWallet: EwalletProductResponse? = null,
            walletBalance: EwalletBalanceResponse? = null
        ) {
            val intent = Intent(caller, InquiryDompetDigitalReskinActivity::class.java)
            intent.putExtra(TAG_RESPONSE, Gson().toJson(inquiryDompetDigitalResponse))
            intent.putExtra(TAG_WALLET_INFO, Gson().toJson(selectedWallet))
            intent.putExtra(TAG_PHONE_NUMBER, phoneNumber)
            if (walletBalance != null) {
                intent.putExtra(TAG_WALLET_BALANCE, Gson().toJson(walletBalance))
            }
            mUrlConfirm = urlConfirm
            mUrlPayment = urlPayment
            isFromFastMenu = fromFastMenu
            mNominal = nominal
            mPhoneNumber = phoneNumber
            mSelectedWallet = selectedWallet
            mWalletBalance = walletBalance
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityInquiryDompetDigitalReskinBinding.inflate(layoutInflater)
        setContentView(binding.root)

        if (intent.extras != null) {
            parseIntent()
        }

        injectDependency()
        setupView()
    }

    override fun dispatchTouchEvent(ev: android.view.MotionEvent?): Boolean {
        if (ev?.action == android.view.MotionEvent.ACTION_DOWN) {
            // Check if touch is outside the keypad and etNominal
            val keypadRect = android.graphics.Rect()
            val etNominalRect = android.graphics.Rect()

            binding.keypad.getGlobalVisibleRect(keypadRect)
            binding.etNominal.getGlobalVisibleRect(etNominalRect)

            val touchX = ev.rawX.toInt()
            val touchY = ev.rawY.toInt()

            val touchedKeypad = keypadRect.contains(touchX, touchY)
            val touchedEtNominal = etNominalRect.contains(touchX, touchY)

            // If touched outside both keypad and etNominal, hide keypad and clear focus
            if (!touchedKeypad && !touchedEtNominal && binding.keypad.visibility == android.view.View.VISIBLE) {
                binding.keypad.animateHide()
                binding.etNominal.clearFocus()
                hideNativeKeyboard()
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    private fun parseIntent() {
        if (intent.extras != null) {
            if (intent.hasExtra(TAG_RESPONSE)) {
                mInquiryDompetRevampResponse = Gson().fromJson(
                    intent.extras?.getString(TAG_RESPONSE),
                    InquiryDompetDigitalResponse::class.java
                )
            }

            if (intent.hasExtra(TAG_WALLET_INFO)) {
                mSelectedWallet = Gson().fromJson(
                    intent.extras?.getString(TAG_WALLET_INFO),
                    EwalletProductResponse::class.java
                )
            }

            if (intent.hasExtra(TAG_PHONE_NUMBER)) {
                mPhoneNumber = intent?.extras?.getString(TAG_PHONE_NUMBER, "") ?: ""
            }

            if (intent.hasExtra(TAG_WALLET_BALANCE)) {
                mWalletBalance = Gson().fromJson(
                    intent.extras?.getString(TAG_WALLET_BALANCE),
                    EwalletBalanceResponse::class.java
                )
            }
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.isFromFastMenu(isFromFastMenu)
        presenter.setUrlConfirm(mUrlConfirm)
        presenter.getAccountList()
        presenter.start()
    }

//    TODO Handle implementation of custom numpad (on progress)
//    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
//        val view = currentFocus
//
//        if (ev.action == MotionEvent.ACTION_DOWN && view is EditText) {
//            val outRect = Rect()
//            view.getGlobalVisibleRect(outRect)
//
//            val tappedOutsideEditText = !outRect.contains(ev.rawX.toInt(), ev.rawY.toInt())
//            val tappedOutsideNumpad = !numpadHelper.isTouchInsideNumpad(ev)
//
//            // ❗ hanya hide jika klik di luar EditText DAN di luar numpad
//            if (tappedOutsideEditText && tappedOutsideNumpad) {
//                view.clearFocus()
//                numpadHelper.hideKeyboard()
//            }
//        }
//
//        return super.dispatchTouchEvent(ev)
//    }

    private fun setupView() {

        GeneralHelperNewSkin.setToolbar(
            this, binding.toolbar.toolbar,
            GeneralHelper.getString(R.string.ewallet)
        )

        val trimmedNumber = mPhoneNumber.replaceFirst(
            "^0+(?=\\d)".toRegex(),
            GeneralHelper.getString(R.string.hint_prefix_62)
        )

        // Create SpannableString to apply different colors to different parts
        val spannablePhoneNumber = SpannableString(trimmedNumber)
        val prefix = GeneralHelper.getString(R.string.hint_prefix_62)

        if (trimmedNumber.startsWith(prefix)) {
            // Apply disabled color to the "+62" prefix
            spannablePhoneNumber.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(this, R.color.text_disabled_default_ns)),
                0,
                prefix.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            // Apply black color to the rest of the number
            spannablePhoneNumber.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(this, R.color.text_black_default_ns)),
                prefix.length,
                trimmedNumber.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        binding.etNoPelanggan.setText(spannablePhoneNumber)
        binding.tvHint.setText(GeneralHelper.getString(R.string.txt_nomor_hp))

        binding.etNoPelanggan.isEnabled = false

        binding.etNominal.setFilters(arrayOf(InputFilter.LengthFilter(NOMINAL_CHARACTER_LIMIT)))

        // Set nominal if hit inquiry from history
        if (mNominal != "") {
            // Set only the numeric value, prefixText will handle "Rp"
            val formattedNominal = GeneralHelper.formatNominalBiasa(mNominal.toDouble())

            // Find matching OptionAmountItem to get the properly formatted nominalString
            val matchingItem = mInquiryDompetRevampResponse.optionAmount.find {
                it.value.toString() == mNominal
            }

            // Use the formatted name from OptionAmountItem if found, otherwise create a fallback format
            nominalString = matchingItem?.name ?: "Rp${GeneralHelper.formatNominal(mNominal)}"

            binding.etNominal.setText(formattedNominal)
            binding.ivClearNominal.visibility = View.VISIBLE
        } else {
            binding.etNominal.setText("")
        }

        binding.etNote.apply {
            setInputType(InputType.TYPE_CLASS_TEXT)
            // Set character limit
            setFilters(arrayOf(InputFilter.LengthFilter(NOTE_CHARACTER_LIMIT)))
            // Initialize character counter
            updateNoteCounter()
            // Add text watcher for character counter
            addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    binding.etNote.removeAllEndIcons()
                    if (s != null && s.isNotEmpty()) {
                        binding.etNote.addEndIcon(R.drawable.ic_clear_ns, sizeDp = 24) {
                            binding.etNote.clearText()
                        }
                    }
                    updateNoteCounter()
                }
            })
        }

        mbrivaOpenResponse = mInquiryDompetRevampResponse.billingDetailOpen
        feeAdminString = mInquiryDompetRevampResponse.adminFee.toString()

        for (billingDetailOpen in mbrivaOpenResponse)
            openModel = billingDetailOpen

        // Load wallet icon - use selected wallet info if available, otherwise use openModel
        val selectedWallet = mSelectedWallet
        if (selectedWallet != null) {
            // Load icon from selected connected wallet
            GeneralHelper.loadIconTransaction(
                this@InquiryDompetDigitalReskinActivity,
                selectedWallet.iconPath,
                selectedWallet.iconName?.split(".")?.get(0) ?: "",
                binding.ivWalletIcon,
                GeneralHelper.getImageId(this, "ic_menu_qna_dompet_digital")
            )

            // Set wallet balance if available
            val walletBalance = mWalletBalance
            if (walletBalance != null) {
                binding.tvBalanceWallet.text = walletBalance.value_string ?: "Rp 0"
                binding.tvBalanceWallet.visibility = View.VISIBLE
            } else {
                binding.tvBalanceWallet.text = "Rp 0"
                binding.tvBalanceWallet.visibility = View.VISIBLE
            }
        } else {
            // Load icon from inquiry response
            GeneralHelper.loadIconTransaction(
                this@InquiryDompetDigitalReskinActivity,
                openModel.iconPath,
                openModel.iconName?.split("\\.")?.get(0) ?: "",
                binding.ivWalletIcon,
                0
            )

            // Hide balance for regular inquiry
            binding.tvBalanceWallet.visibility = View.GONE
        }

        binding.iconContainer.visibility = View.VISIBLE

        //set minimum
        if (mInquiryDompetRevampResponse.minimumTransactionString.isNotEmpty()) {
            minTrx = mInquiryDompetRevampResponse.minimumTransaction
            minTrxString = mInquiryDompetRevampResponse.minimumTransactionString
        }

        //set maximum
        if (mInquiryDompetRevampResponse.maximumTransactionString.isNotEmpty()) {
            maxTrx = mInquiryDompetRevampResponse.maximumTransaction
            maxTrxString = mInquiryDompetRevampResponse.maximumTransactionString
        }

        binding.helperText.setText(
            String.format(
                GeneralHelper.getString(R.string.minimal_rp),
                minTrxString
            )
        )

        saveStr = mInquiryDompetRevampResponse.nameDefault

        adapter = RekomendasiTopUpAdapterReskin(
            onItemClick = { selectedItem ->
                // Set only the numeric value, prefixText will handle "Rp"
                val formattedAmount =
                    GeneralHelper.formatNominalBiasa(selectedItem.value.toDouble())
                nominalString = selectedItem.name
                binding.etNominal.setText(formattedAmount)
                binding.etNominal.requestFocus()
                binding.etNominal.setSelection(binding.etNominal.text.length)
                checkButton()
            },
            items = mInquiryDompetRevampResponse.optionAmount
        )
        val layoutManager = GridLayoutManager(
            applicationContext, 2
        )
        binding.rvOptionAmount.layoutManager = layoutManager
        binding.rvOptionAmount.itemAnimator = DefaultItemAnimator()
        binding.rvOptionAmount.adapter = adapter

        initiateViews()
        setupSumberDanaView()

        // Trigger adapter selection and button check if nominal was set from history
        if (mNominal != "") {
            // Post to ensure text watchers are set up first
            binding.etNominal.post {
                updateAdapterSelection()
            }
        }

        setupKeypad()
    }

    private fun setupKeypad() = with(binding) {
        // Don't use the default attachToEditText, instead create custom keypad handling
        setupCustomKeypadForNominal()
    }

    private fun setupCustomKeypadForNominal() {
        binding.keypad.setOnKeypadListener(object : id.co.bri.brimo.ui.customviews.keypad.KeypadView.KeypadViewListener {
            override fun onValuePadClicked(value: String) {
                android.util.Log.d("CustomKeypad", "Value clicked: '$value'")

                // Get current text and add the new digit
                val currentText = binding.etNominal.text.toString()
                val cleanCurrent = currentText.replace(".", "").replace(",", "")
                val newCleanText = cleanCurrent + value

                android.util.Log.d("CustomKeypad", "Current: '$currentText', Clean: '$cleanCurrent', New: '$newCleanText'")

                // Check if the new clean text would exceed the limit (12 digits = 999.999.999.999)
                if (newCleanText.length > 12) {
                    android.util.Log.d("CustomKeypad", "Input limit reached, ignoring")
                    return
                }

                // Format the new text
                if (newCleanText.isNotEmpty() && newCleanText.all { it.isDigit() }) {
                    val formattedText = GeneralHelper.formatNominalForKeypad(newCleanText.toDouble())
                    android.util.Log.d("CustomKeypad", "Setting formatted text: '$formattedText'")

                    // Check if formatted text would exceed EditText maxLength
                    if (formattedText.length > 15) {
                        android.util.Log.d("CustomKeypad", "Formatted text too long, ignoring")
                        return
                    }

                    // Remove text watcher temporarily to avoid conflicts
                    binding.etNominal.removeTextChangedListener(unifiedTextWatcher)

                    // Set the formatted text
                    binding.etNominal.setText(formattedText)

                    // Set cursor position with bounds checking
                    try {
                        val actualLength = binding.etNominal.text?.length ?: 0
                        val cursorPosition = minOf(formattedText.length, actualLength)
                        if (cursorPosition >= 0 && cursorPosition <= actualLength) {
                            binding.etNominal.setSelection(cursorPosition)
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("CustomKeypad", "Error setting cursor position", e)
                    }

                    // Re-add text watcher
                    binding.etNominal.addTextChangedListener(unifiedTextWatcher)

                    // Manually trigger UI updates since we bypassed the text watcher
                    binding.ivClearNominal.visibility = View.VISIBLE
                    updateAdapterSelection()
                    checkButton()

                    android.util.Log.d("CustomKeypad", "Final text: '${binding.etNominal.text}'")
                }
            }

            override fun onDeletePadClicked() {
                android.util.Log.d("CustomKeypad", "Delete clicked")

                val currentText = binding.etNominal.text.toString()
                val cleanCurrent = currentText.replace(".", "").replace(",", "")

                if (cleanCurrent.isNotEmpty()) {
                    val newCleanText = cleanCurrent.dropLast(1)

                    android.util.Log.d("CustomKeypad", "Delete: '$cleanCurrent' -> '$newCleanText'")

                    // Remove text watcher temporarily
                    binding.etNominal.removeTextChangedListener(unifiedTextWatcher)

                    if (newCleanText.isNotEmpty() && newCleanText.all { it.isDigit() }) {
                        val formattedText = GeneralHelper.formatNominalForKeypad(newCleanText.toDouble())

                        binding.etNominal.setText(formattedText)

                        // Set cursor position with bounds checking
                        try {
                            val actualLength = binding.etNominal.text?.length ?: 0
                            val cursorPosition = minOf(formattedText.length, actualLength)
                            if (cursorPosition >= 0 && cursorPosition <= actualLength) {
                                binding.etNominal.setSelection(cursorPosition)
                            }
                        } catch (e: Exception) {
                            android.util.Log.e("CustomKeypad", "Error setting cursor position", e)
                        }
                    } else {
                        binding.etNominal.setText("")
                    }

                    // Re-add text watcher
                    binding.etNominal.addTextChangedListener(unifiedTextWatcher)

                    // Manually trigger UI updates since we bypassed the text watcher
                    val finalText = binding.etNominal.text.toString()
                    binding.ivClearNominal.visibility = if (finalText.isNotEmpty()) View.VISIBLE else View.GONE
                    updateAdapterSelection()
                    checkButton()
                }
            }
        })

        // Disable native keyboard and show custom keypad
        binding.etNominal.apply {
            // Disable native keyboard
            showSoftInputOnFocus = false

            setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    // Hide native keyboard if it's showing
                    hideNativeKeyboard()

                    // Show custom keypad
                    binding.keypad.animateShow()
                } else {
                    // Hide custom keypad when focus is lost
                    binding.keypad.animateHide()
                }
            }

            setOnClickListener {
                // Request focus and show keypad
                requestFocus()
            }
        }

        // Handle etNote focus to hide custom keypad and show native keyboard
        binding.etNote.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                // Hide custom keypad
                binding.keypad.animateHide()
                // Clear focus from etNominal
                binding.etNominal.clearFocus()
            }
        }
    }

    private fun hideNativeKeyboard() {
        val imm = getSystemService(android.content.Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
        imm.hideSoftInputFromWindow(currentFocus?.windowToken, 0)
    }
    private fun setupSumberDanaView() {
        // Configure SumberDanaView properties
        binding.sdnView.isFastMenu = isFromFastMenu

        // Set click listeners for SumberDanaView
        binding.sdnView.setOnClickButton {
            // Safety check to ensure model is not null
            model?.let { accountModel ->
                ConfirmationDompetDigitalReskinActivity.launchIntent(
                    this,
                    openModel,
                    mInquiryDompetRevampResponse.saved,
                    mInquiryDompetRevampResponse.referenceNumber,
                    accountModel,
                    binding.etNote.getText(),
                    nominalStrClr,
                    nominalString,
                    mInquiryDompetRevampResponse.adminFee.toString(),
                    mInquiryDompetRevampResponse.adminFeeString,
                    mUrlConfirm,
                    mUrlPayment,
                    isFromFastMenu
                )
            }
        }

        binding.sdnView.setOnClickSumberDana {
            gantiSumberDana()
        }
    }

    private fun initiateViews() {
        // Create a unified text watcher that handles both formatting and selection
        unifiedTextWatcher = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

            override fun afterTextChanged(s: Editable?) {
                val currentText = s.toString()
                android.util.Log.d("TextWatcher", "afterTextChanged: '$currentText'")

                // Show/hide clear button based on content
                binding.ivClearNominal.visibility =
                    if (currentText.isNotEmpty()) View.VISIBLE else View.GONE

                // Update adapter selection and button state
                updateAdapterSelection()

                // Update button state
                checkButton()
            }
        }

        binding.etNominal.addTextChangedListener(unifiedTextWatcher)

        // Handle clear button click
        binding.ivClearNominal.setOnClickListener {
            binding.etNominal.setText("")
            binding.etNominal.requestFocus()
            binding.helperText.apply {
                setText(
                    String.format(
                        GeneralHelper.getString(R.string.minimal_rp),
                        minTrxString
                    )
                )
                setTextColor(
                    ContextCompat.getColor(
                        this@InquiryDompetDigitalReskinActivity,
                        R.color.text_gray_default_ns
                    )
                )
            }
            binding.sdnView.payAmount = 0
            disableButton(true)
        }
    }

    override fun afterText(editable: Editable?) {
        updateAdapterSelection()
    }

    private fun updateAdapterSelection() {
        clearSelected()

        val normalizedInput = getNormalizedAmount()

        val index = mInquiryDompetRevampResponse.optionAmount.indexOfFirst {
            it.value.toString() == normalizedInput
        }

        mInquiryDompetRevampResponse.optionAmount.forEachIndexed { i, item ->
            item.setSelected(i == index)
        }

        // Update nominalString if a matching item is found
        if (index != -1) {
            nominalString = mInquiryDompetRevampResponse.optionAmount[index].name
        } else {
            // If no match found and input is not empty, create formatted string
            if (normalizedInput.isNotEmpty() && normalizedInput.all { it.isDigit() }) {
                nominalString = "Rp${GeneralHelper.formatNominal(normalizedInput)}"
            }
        }

        adapter.setSelectedPosition(index)
        adapter.notifyDataSetChanged()
        checkButton()
    }

    override fun onExceptionTrxExpired(desc: String) {
        val returnIntent = Intent()
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, desc)
        this.setResult(RESULT_CANCELED, returnIntent)
        finish()
    }

    override fun onException(message: String) {
        GeneralHelperNewSkin.triggerVibration(this, Constant.VIBRATE_ERROR)
        if (GeneralHelperNewSkin.isContains(Constant.LIST_TYPE_GAGAL_GANGGUAN_SISTEM, message)) {
            GeneralHelperNewSkin.showErrorBottomDialog(this, message)
        } else {
            showSnackbarErrorMessage(message, ALERT_ERROR, this, false)
        }
    }

    private fun checkButton() {
        val rawNominal = binding.etNominal.text.toString()
        nominalStrClr = if (rawNominal.isEmpty()) "-"
        else rawNominal.replace(".", "").trim()

        val nominal = nominalStrClr.toLongOrNull()

        // Invalid input or empty
        if (rawNominal.isEmpty() || nominal == null || nominal == 0L) {
            disableButton(true)
            return
        }

        // Check if model is null (account data not loaded yet)
        val accountModel = model
        if (accountModel == null) {
            disableButton(true)
            return
        }

        val minRequired = BigInteger.valueOf(minTrx)
        val maxNominal = BigInteger.valueOf(maxTrx)
        val isMaxNominalLargerThanZero = maxNominal > BigInteger.ZERO
        val currentNominal = BigInteger.valueOf(nominal)
        val maxAllowed = BigInteger.valueOf(saldo.toLong() - getMinBalance(accountModel))

        // Update SumberDanaView with payment amount
        binding.sdnView.payAmount = nominal?.toInt() ?: 0

        binding.helperText.apply {
            when {
                currentNominal > maxAllowed -> {
                    setText(
                        String.format(
                            GeneralHelper.getString(R.string.txt_saldo_tidak_cukup_helper),
                            minTrxString
                        )
                    )
                    setTextColor(
                        ContextCompat.getColor(
                            this@InquiryDompetDigitalReskinActivity,
                            R.color.red_ns_main
                        )
                    )
                }

                currentNominal < minRequired -> {
                    setText(
                        String.format(
                            GeneralHelper.getString(R.string.text_minimal_top_up),
                            minTrxString
                        )
                    )
                    setTextColor(
                        ContextCompat.getColor(
                            this@InquiryDompetDigitalReskinActivity,
                            R.color.red_ns_main
                        )
                    )
                }

                isMaxNominalLargerThanZero -> {
                    if (currentNominal > maxNominal) {
                        setText(
                            String.format(
                                GeneralHelper.getString(R.string.text_maximal_top_up),
                                maxTrxString
                            )
                        )
                        setTextColor(
                            ContextCompat.getColor(
                                this@InquiryDompetDigitalReskinActivity,
                                R.color.red_ns_main
                            )
                        )
                    } else {
                        setText(
                            String.format(
                                GeneralHelper.getString(R.string.minimal_rp),
                                minTrxString
                            )
                        )
                        setTextColor(
                            ContextCompat.getColor(
                                this@InquiryDompetDigitalReskinActivity,
                                R.color.text_gray_default_ns
                            )
                        )
                    }
                }

                else -> {
                    setText(
                        String.format(
                            GeneralHelper.getString(R.string.minimal_rp),
                            minTrxString
                        )
                    )
                    setTextColor(
                        ContextCompat.getColor(
                            this@InquiryDompetDigitalReskinActivity,
                            R.color.text_gray_default_ns
                        )
                    )
                }
            }
        }

        when {
            currentNominal < minRequired -> disableButton(true)
            currentNominal > maxNominal -> disableButton(isMaxNominalLargerThanZero)
            currentNominal > maxAllowed -> disableButton(!isFromFastMenu)
            else -> disableButton(false)
        }
    }

    private fun disableButton(disable: Boolean) {
        val shouldEnable = !disable
        binding.sdnView.isEnableButton(shouldEnable)
    }

    override fun onSelectSumberDana(bankModel: AccountModel) {
        binding.sdnView.account = bankModel
        model = bankModel
        // Update saldo from the selected account
        saldo = bankModel.saldoReponse?.balance ?: 0.0
        checkButton()
    }

    override fun onSendFailedList(list: MutableList<Int>?) {
        mListFailed = list
    }

    private fun gantiSumberDana() {
        counter++
        val accountList = mListAccountModel
        if (accountList == null) {
            GeneralHelper.showToast(this, GeneralHelper.getString(R.string.no_account_list))
        } else {
            val selectedIndex = accountList.indexOfFirst {
                model?.acoountString.equals(it.acoountString)
            }

            val rawNominal = binding.etNominal.text.toString()
            val nominalStr = if (rawNominal.isEmpty()) "0"
            else rawNominal.replace(".", "").trim()
            val nominalInt = nominalStr.toInt()

            val fragmentSumberDana =
                SumberDanaFragment(
                    accountList, this, counter, mListFailed, selectedIndex, nominalInt,
                    isFromFastMenu
                )
            fragmentSumberDana.show(supportFragmentManager, Constant.TAG_PICK_ACCOUNT)
        }
    }

    @Deprecated("Deprecated in Java")
    @Suppress("DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else {
                if (data != null) {
                    this.setResult(RESULT_CANCELED, data)
                    finish()
                }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun clearSelected() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            mInquiryDompetRevampResponse.optionAmount.forEach(Consumer { p: OptionAmountItem ->
                p.isSelected = false
            })
        } else {
            for (p in mInquiryDompetRevampResponse.optionAmount) {
                p.isSelected = false
            }
        }
        adapter.notifyDataSetChanged()
    }

    private fun getNormalizedAmount(): String {
        val raw = binding.etNominal.text.toString()
        return raw.replace(".", "")
            .replace(",", "")
            .trim()
    }

    private fun getMinBalance(model: AccountModel): Long {
        val tempAccountModel =
            mListAccountModel?.firstOrNull { it.acoount == model.acoount } ?: AccountModel()
        return when {
            tempAccountModel.minimumBalance != null && tempAccountModel.minimumBalance >= 0 -> tempAccountModel.minimumBalance.toLong()
            model.minimumBalance != null && model.minimumBalance >= 0 -> model.minimumBalance.toLong()
            else -> 0L
        }
    }

    override fun onSwitchChange(isChecked: Boolean) {
        // Do nothing
    }

    private fun updateNoteCounter() {
        val currentLength = binding.etNote.getText().length
        binding.tvNoteCounter.text = "$currentLength/$NOTE_CHARACTER_LIMIT"
    }

    override fun onSuccessAccountList(
        accountList: MutableList<AccountModel>, mainAccount: AccountModel
    ) {
        model = mainAccount
        binding.sdnView.account = mainAccount
        mListAccountModel = accountList
        // Update saldo from the main account
        saldo = mainAccount.saldoReponse?.balance ?: 0.0
        // Trigger initial button check to set payAmount
        checkButton()
    }
}