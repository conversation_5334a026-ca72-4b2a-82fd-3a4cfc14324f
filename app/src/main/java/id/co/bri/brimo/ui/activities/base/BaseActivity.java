package id.co.bri.brimo.ui.activities.base;

import android.Manifest;
import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.graphics.Rect;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;

import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;

import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.fragment.app.FragmentTransaction;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentTransaction;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.appsflyer.AppsFlyerLib;
import com.appsflyer.attribution.AppsFlyerRequestListener;
import com.google.android.material.textfield.TextInputEditText;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.gson.Gson;
import com.zoloz.webcontainer.plugin.PermissionCallback;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import java.util.List;
import java.util.Map;

import id.co.bri.brimo.BaseApp;
import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.di.components.ActivityComponent;
import id.co.bri.brimo.di.components.DaggerActivityComponent;
import id.co.bri.brimo.di.modules.ActivityModule;
import id.co.bri.brimo.domain.SnackBarPosition;
import id.co.bri.brimo.domain.SnackBarType;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.DeviceIDHelper;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin;
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralResponse;
import id.co.bri.brimo.models.apimodel.response.MaintenanceAlert;
import id.co.bri.brimo.security.MyCryptStatic;
import id.co.bri.brimo.ui.activities.FastMenuActivity;
import id.co.bri.brimo.ui.activities.FastMenuNewSkinActivity;
import id.co.bri.brimo.ui.activities.alertmaintenance.AlertMaintenanceActivity;
import id.co.bri.brimo.ui.activities.lupapassword.FormLupaPasswordActivity;
import id.co.bri.brimo.ui.activities.transactionlimitinformation.TransactionLimitInformationActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;
import id.co.bri.brimo.ui.customviews.dialog.DialogSetDefault;
import id.co.bri.brimo.ui.customviews.dialog.DialogSetDefault.DialogDefaultListener;
import id.co.bri.brimo.ui.customviews.keypad.KeypadView;
import id.co.bri.brimo.ui.fragments.BottomFragmentImage;
import id.co.bri.brimo.ui.fragments.FragmentBottomDialog;
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment;
import id.co.bri.brimo.ui.widget.BrimoAppsWidget;
import id.co.bri.brimo.util.LocaleUtilKt;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

public abstract class BaseActivity extends AppCompatActivity
        implements
        IMvpView,
        DialogDefaultListener {


    public static final int ALERT_CONFIRM = -1;
    public static final int ALERT_ERROR = -2;
    public static final int ANIMATE_SHOW = -10;
    public static final int ANIMATE_INVISIBLE = -11;
    public static final int ANIMATE_GONE = -12;
    protected boolean isAnimatedShow = false;
    protected boolean isAnimatedGone = false;
    protected boolean mIsRooted = false;
    protected boolean isAnimatedInvisible = false;
    protected Animation animationUp;
    protected Animation animationDown;
    protected Animation animationFadeIn;
    protected Animation animationFadeOut;
    protected long mLastClickTime = 0;
    protected DialogSetDefault dialogSetDefault;
    protected FirebaseAnalytics mFirebaseAnalytics;
    protected static boolean isFromFastMenu = false;
    protected static boolean mIsFromTopUpOnline = false;
    protected boolean isUseTouchDispatchFunc = true;
    protected static boolean mIsNotValid = false;
    protected boolean isSessionEnd = false;
    protected boolean checkAccessibilityService = true;
    private static final String ALPHA = "alpha";
    private static final String UPDATE_WIDGET = "android.appwidget.action.APPWIDGET_UPDATE";

    protected static final int PERMISSIONS_ONBOARDING = 106;


    public static String[] storge_permissions = {
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE
    };

    @RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
    public static String[] storge_permissions_33 = {
            Manifest.permission.READ_MEDIA_IMAGES,
            Manifest.permission.READ_MEDIA_AUDIO,
            Manifest.permission.READ_MEDIA_VIDEO
    };

    @RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
    public static String[] notif_permissions_33 = {
            Manifest.permission.POST_NOTIFICATIONS
    };

    public static String[] permissions() {
        String[] permission;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permission = storge_permissions_33;
        } else {
            permission = storge_permissions;
        }
        return permission;
    }

    protected String[] PERMISSIONS = permissions();
    protected static final int PERMISSIONS_ALL = 1;

    private ActivityComponent mActivityComponent;

    protected BroadcastReceiver updates_receiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String filter = Constant.TAG_NOTIF;
            if (intent.getAction().equals(filter)) {
                parseDataNotifForeground(intent);  //update UI atau data
            }
        }
    };

    /**
     * Method untuk menampilkan/hilangkan snackbar error dengan jeda waktu 2.75 detik
     *
     * @param isError true jika ingin menampilkan snackbar, false jika ingin menghilangkan
     */
    protected void isOnErrorSnackBarShow(boolean isError) {
        Handler handler = new Handler(Looper.getMainLooper());
        Runnable runnable = () -> isOnErrorSnackBarShow(false);
        if (isError) handler.postDelayed(runnable, 3000);
        else  handler.removeCallbacks(runnable);
    }

    /**
     * Parse data notif foreground
     *
     * @param intent
     */
    protected void parseDataNotifForeground(Intent intent) {
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);

        GeneralHelper.setHelperContext(this);
        DeviceIDHelper.setHelperContext(this);

        //init Activity Component
        initActivityComponent();

        //initiate common animation
        initiateAnimation();

        //reset animation
        isAnimatedGone = false;
        isAnimatedShow = false;
        isAnimatedInvisible = false;

        handleBackPressCustomKeypad();
    }

    private void handleBackPressCustomKeypad() {
        getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                try {
                    KeypadView keypad = (KeypadView) findMyCustomKeypadView();
                    if (keypad != null && keypad.isShown()) {
                        keypad.animateHide();
                    } else {
                        setEnabled(false);
                        getOnBackPressedDispatcher().onBackPressed();
                    }
                } catch (Exception e) {
                    setEnabled(false);
                    getOnBackPressedDispatcher().onBackPressed();
                }
            }
        });
    }

    @Override
    protected void attachBaseContext(Context newBase) {
        String newLang = LocaleUtilKt.getSavedLanguage(newBase);
        super.attachBaseContext(LocaleUtilKt.applyLanguageContext(newBase, newLang));
    }

    protected void initiateAnimation() {
        //initiate animation
        animationUp = AnimationUtils.loadAnimation(getApplicationContext(), R.anim.slide_up_hide);
        animationDown = AnimationUtils.loadAnimation(getApplicationContext(), R.anim.slide_down_show);
        animationFadeIn = AnimationUtils.loadAnimation(getApplicationContext(), R.anim.fade_in_trasition);
        animationFadeOut = AnimationUtils.loadAnimation(getApplicationContext(), R.anim.fade_out_trasition);
    }

    protected void initActivityComponent() {
        mActivityComponent = DaggerActivityComponent.builder()
                .activityModule(new ActivityModule(this))
                .applicationComponent(((BaseApp) getApplication()).getComponent())
                .build();
    }

    public ActivityComponent getActivityComponent() {
        if (mActivityComponent == null) {
            initActivityComponent();
        }

        return mActivityComponent;
    }

    public void showSnackbarErrorMessage(String message, int messageType, Activity activity, boolean isFragment) {
        try {
            switch (messageType) {
                case ALERT_CONFIRM:
                    GeneralHelper.showSnackBarGreen(findViewById(R.id.content), message);
                    break;
                case ALERT_ERROR:
                    GeneralHelper.showSnackBar(findViewById(R.id.content), message);
                    break;
            }
        } catch (Exception e) {
            //do nothing
        }
    }

    public void showSnackbarErrorMessageRevamp(String message, int messageType, Activity activity, boolean isFragment) {
        try {
            switch (messageType) {
                case ALERT_CONFIRM:
                    GeneralHelperNewSkin.INSTANCE.showSnackBarCustom(activity, message, SnackBarType.SUCCESS, SnackBarPosition.TOP);
//                    GeneralHelper.showSnackBarGreenRevamp(findViewById(R.id.content), message);
                    break;
                case ALERT_ERROR:
                    GeneralHelperNewSkin.INSTANCE.showSnackBarCustom(activity, message, SnackBarType.ERROR, SnackBarPosition.TOP);
//                    GeneralHelper.showSnackBarRevamp(findViewById(R.id.content), message);
                    break;
                default:
                    return;
            }
        } catch (Exception e) {
            //do nothing
        }
    }

    protected TextWatcher activityTextListener = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            beforeText();
        }

        @Override
        public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            changeText(charSequence, i, i1, i2);
        }

        @Override
        public void afterTextChanged(Editable editable) {
            afterText(editable);
        }
    };

    protected void changeText(CharSequence charSequence, int i, int i1, int i2) {
    }

    protected void afterText(Editable editable) {
    }

    protected void beforeText() {
    }

    public static boolean hasPermissions(Context context, String... permissions) {
        if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && context != null && permissions != null) {
            for (String permission : permissions) {
                if (ActivityCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * Method untuk menampilkan progress circle ketika call service
     */
    @Override
    public void showProgress() {
        GeneralHelper.showDialog(this);
    }

    /**
     * Method untuk menghilangkan progress circle ketika call service berakhir
     */
    @Override
    public void hideProgress() {
        GeneralHelper.dismissDialog();
    }

    /**
     * Method ketika session dari BE berakhir
     *
     * @param message deskripsi session habis dari BE
     */
    @Override
    public void onSessionEnd(String message) {
        hideProgress();
//        FastMenuActivity.launchIntentSessionEnd(this, message);
        FastMenuNewSkinActivity.launchIntentSessionEnd(this, message);
    }

    /**
     * Method ini untuk menghandle error RC 12
     *
     * @param message deskripsi error dari BE
     */
    @Override
    public void onException(String message) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
            GeneralHelper.showDialogGagalBack(this, message);
        else {
            showSnackbarErrorMessageRevamp(message, ALERT_ERROR, this, false);
            isOnErrorSnackBarShow(true);
        }
    }

    @Override
    public void onExceptionRevamp(String message) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
            GeneralHelper.showDialogGagalBack(this, message);
        else {
            showSnackbarErrorMessageRevamp(message, ALERT_ERROR, this, false);
            isOnErrorSnackBarShow(true);
        }
    }

    @Override
    public void onException99(String message) {
//        FastMenuActivity.launchIntentDialogBox(this, message);
        FastMenuNewSkinActivity.launchIntentDialogBox(this, message);
    }

    @Override
    public void onException06(ExceptionResponse response) {
        GeneralHelper.showDialogCustomBack(this, response);
    }

    /**
     * Handles the RC SM error during the onboarding process, triggered when the user's registration status
     * does not match the database record.
     */
    @Override
    public void onExceptionStatusNotMatch() {
        Intent intent = new Intent();
        intent.putExtra(Constant.STATUS_NOT_MATCH, Constant.STATUS_NOT_MATCH);
        intent.putExtra(Constant.NAME, Constant.REGISTRATION_BRIMO);
        intent.putExtra(Constant.CHECK_POINT, Constant.CHECK_POINT_DEFAULT);
        setResult(Activity.RESULT_CANCELED, intent);
        finish();
    }

    protected void showAlertFinish(String msg) {
        AlertDialog.Builder alert = new AlertDialog.Builder(this);
        alert.setMessage(msg).setCancelable(false).setPositiveButton("OK", (dialog, which) -> {
            // TODO Auto-generated method stub
            dialog.dismiss();
            finish();
        }).show();
    }

    protected View findMyCustomKeypadView() {
        return null;
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN && isUseTouchDispatchFunc) {

            View v = getCurrentFocus();
            View newTargetView = findViewAt(event.getRawX(), event.getRawY());
            if (v instanceof EditText && !(newTargetView instanceof EditText)) {
                View customKeypadView = findMyCustomKeypadView();

                Rect outRectEditText = new Rect();
                v.getGlobalVisibleRect(outRectEditText);
                boolean isTouchOutsideEditText = !outRectEditText.contains((int) event.getRawX(), (int) event.getRawY());
                boolean isTouchOnCustomKeypad = false;

                if (customKeypadView != null && customKeypadView.getVisibility() == View.VISIBLE) {
                    Rect outRectKeypad = new Rect();
                    customKeypadView.getGlobalVisibleRect(outRectKeypad);
                    if (outRectKeypad.contains((int) event.getRawX(), (int) event.getRawY())) {
                        isTouchOnCustomKeypad = true;
                    }
                }

                if (isTouchOutsideEditText && !isTouchOnCustomKeypad) {
                    v.clearFocus();
                    InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                    imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
                }
            }
        }

        try {
            return super.dispatchTouchEvent(event);
        } catch (Exception e) {
            return false;
        }
    }

    private View findViewAt(float rawX, float rawY) {
        View root = getWindow().getDecorView();
        int[] location = new int[2];
        root.getLocationOnScreen(location);
        return findViewAtRecursive(root, (int) rawX, (int) rawY);
    }

    private View findViewAtRecursive(View view, int x, int y) {
        if (!(view instanceof ViewGroup)) {
            Rect rect = new Rect();
            view.getGlobalVisibleRect(rect);
            return rect.contains(x, y) ? view : null;
        }
        ViewGroup group = (ViewGroup) view;
        for (int i = 0; i < group.getChildCount(); i++) {
            View found = findViewAtRecursive(group.getChildAt(i), x, y);
            if (found != null) return found;
        }
        return null;
    }

    //ANIMATION
    protected void onAnimator(List<View> views, boolean isVisible, int typeAnimation, String tagIdAnimate) {
        if (views.size() > 0) {
            for (View currentView : views) {
                if (typeAnimation == ANIMATE_SHOW && !isAnimatedShow) {
                    onAnimatorShow(currentView, isVisible, tagIdAnimate);
                    isAnimatedShow = true;
                    isAnimatedInvisible = false;
                    isAnimatedGone = false;
                }

                if (typeAnimation == ANIMATE_GONE && !isAnimatedGone) {
                    onAnimatorFade(currentView, isVisible, tagIdAnimate);
                    isAnimatedGone = true;
                    isAnimatedShow = false;
                }

                if (typeAnimation == ANIMATE_INVISIBLE && !isAnimatedInvisible) {
                    onAnimatorInvisible(currentView, isVisible);
                    isAnimatedInvisible = true;
                    isAnimatedShow = false;
                }
            }
        }
    }

    protected void onAnimator(View view, boolean isVisible, int typeAnimation, String tagIdAnimate) {
        if (view != null) {

            if (typeAnimation == ANIMATE_SHOW && !isAnimatedShow) {
                onAnimatorShow(view, isVisible, tagIdAnimate);
                isAnimatedShow = true;
                isAnimatedInvisible = false;
                isAnimatedGone = false;
            }

            if (typeAnimation == ANIMATE_GONE && !isAnimatedGone) {
                onAnimatorFade(view, isVisible, tagIdAnimate);
                isAnimatedGone = true;
                isAnimatedShow = false;
            }

            if (typeAnimation == ANIMATE_INVISIBLE && !isAnimatedInvisible) {
                onAnimatorInvisible(view, isVisible);
                isAnimatedInvisible = true;
                isAnimatedShow = false;
            }
        }
    }

    public void onAnimatorShow(View view, boolean isVisible, String tagId) {
        if (isVisible)
            view.setVisibility(View.VISIBLE);

        ObjectAnimator anim = ObjectAnimator.ofFloat(view, ALPHA, 0, 1f);
        anim.setDuration(500).addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                view.setVisibility(View.VISIBLE);
                onAnimatorShowEnd(tagId);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                onAnimatorShowEnd(tagId);
                view.setVisibility(View.VISIBLE);
            }
        });
        anim.start();
    }

    /**
     * Override method ini untuk menangkap callback animation show end
     *
     * @param tagId Id activity request
     */
    protected void onAnimatorShowEnd(String tagId) {
    }

    @Override
    public void onClickYesDefault(int reqId) {
        onClickYesRC99(reqId);
    }

    @Override
    public void onClickNoDefault(int reqId) {
    }

    /**
     * Jika dialog General Error di KLIK
     *
     * @param reqId ID request dari activity yg memanggil
     */
    protected void onClickYesRC99(int reqId) {
        if (reqId == Constant.DIALOG_BASE_GE) {
//            FastMenuActivity.launchIntentError(this, null);
            FastMenuNewSkinActivity.launchIntentError(this, null);
        }
    }

    public void onAnimatorFade(View view, boolean isVisible, String tagIdFade) {
        if (isVisible)
            view.setVisibility(View.VISIBLE);
        else
            view.setVisibility(View.GONE);

        ObjectAnimator anim = ObjectAnimator.ofFloat(view, "alpha", 1f, 0);
        anim.setDuration(500).addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                onAnimatorFadeEnd(tagIdFade);
                view.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                view.setVisibility(View.GONE);
                onAnimatorFadeEnd(tagIdFade);
            }
        });
        anim.start();
    }

    /**
     * Override method ini untuk menangkap callback animation Fade end
     *
     * @param tagIdFade Id activity request
     */
    protected void onAnimatorFadeEnd(String tagIdFade) {
    }

    public void onAnimatorInvisible(View view, boolean isVisible) {
        if (isVisible)
            view.setVisibility(View.VISIBLE);
        else
            view.setVisibility(View.INVISIBLE);

        ObjectAnimator anim = ObjectAnimator.ofFloat(view, "alpha", 1f, 0);
        anim.setDuration(400).addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                view.setVisibility(View.INVISIBLE);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                view.setVisibility(View.INVISIBLE);
            }
        });
        anim.start();
    }

    @Override
    protected void onResume() {
        super.onResume();
        GeneralHelper.setHelperContext(this);

        // check time idle device
        onCheckIdleTime();
    }

    private void onCheckIdleTime() {
        long idleEnd = new Date().getTime();
        long idleTime = idleEnd - mActivityComponent.brimoPrefSource().getTimeIdle();
        Date date = new Date(idleTime);

        if (date.getSeconds() > 1800 && mActivityComponent.brimoPrefSource().getLoginFlag()) {
//            FastMenuActivity.launchIntentIdleDevice(this, true);
            FastMenuNewSkinActivity.launchIntentIdleDevice(this, true);
        }
    }

    @Override
    public void onRootedDevice() {
        mIsRooted = true;
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(this)
                .setTitle("Peringatan")
                .setMessage("Maaf, Anda tidak diijinkan untuk masuk ke aplikasi BRImo karena perangkat Anda terindikasi telah di-root")
                .setNeutralButton("Close", (dialogInterface, i) -> {
                    //finish();
                    finishAffinity();
                })
                .setIcon(R.drawable.ic_close_black_24dp)
                .setCancelable(false);
        alertDialog.show();
    }


    /**
     * Method untuk mengubah warna status bar
     *
     * @param color warna status bar
     */
    public void setStatusColor(int color) {
        if (Build.VERSION.SDK_INT >= 21) {
            Window window = getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.setStatusBarColor(getResources().getColor(color));
        }
    }

    /**
     * Method untuk mengubah warna status bar dan status icon
     *
     * @param color                      warna status bar
     * @param systemUiFlagLightStatusBar tipe icon status bar yang bisa mengubah warna ikon
     */
    public void setStatusColorAndStatusBar(int color, int systemUiFlagLightStatusBar) {
        if (Build.VERSION.SDK_INT >= 21) {
            Window window = getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.setStatusBarColor(getResources().getColor(color));
            window.getDecorView().setSystemUiVisibility(systemUiFlagLightStatusBar);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        LocalBroadcastManager.getInstance(this).unregisterReceiver(updates_receiver);
    }

    /**
     * Start Activity cek service Isolated Service
     */
    @Override
    protected void onStart() {
        super.onStart();
        if (checkAccessibilityService) {
            setAccessibilityServicesProtection(true);
        }
    }

    public void trackAppsFlyerAnalyticEvent(String eventName, Map<String, Object> eventValue) {
        AppsFlyerLib.getInstance().logEvent(this, eventName, eventValue, new AppsFlyerRequestListener() {
            @Override
            public void onSuccess() {
                if (!GeneralHelper.isProd()) {
                    Log.d(Constant.APPSFLYER_TAG, "Event sent successfully");
                }
            }

            @Override
            public void onError(int i, @NonNull String s) {
                if (!GeneralHelper.isProd()) {
                    Log.d(Constant.APPSFLYER_TAG, "Event failed to be sent:\n" +
                            "Error code: " + i + "\n"
                            + "Error description: " + s);
                }
            }
        });
    }

    public void setCustomerUserId(String customerUserId) {
        if (customerUserId.isEmpty()) return;
        AppsFlyerLib.getInstance().setCustomerUserId(customerUserId);
    }

    @Override
    public void onExceptionFO(EmptyStateResponse response) {
        FragmentBottomDialog fragmentBottomDialog = new FragmentBottomDialog(this, Constant.IMAGE_SERVER_UNDER_MAINTENANCE, response.getDescription(), response.getSubDescription(), "img_flag_off", true);
        fragmentBottomDialog.setCancelable(false);
        fragmentBottomDialog.show(getSupportFragmentManager(), "");
    }

    @Override
    public void onExceptionLimitExceed(GeneralResponse response) {
        BottomFragmentImage bottomFragmentLimitExceed = new BottomFragmentImage(
                onCallBackBottomImageLimitExceed(),
                this,
                response.getImagePath(),
                response.getTitle(),
                response.getDescription(),
                GeneralHelper.getString(R.string.see_account_types_and_limit),
                GeneralHelper.getString(R.string.mengerti)
        );
        bottomFragmentLimitExceed.show(getSupportFragmentManager(), "");
    }

    private BottomFragmentImage.OnCallBackBottomImage onCallBackBottomImageLimitExceed() {
        return typeButton -> {
            if (typeButton.equals(BottomFragmentImage.ButtonType.BLUE)) {
                createKotlinFunction0(firstBtnFunction).invoke();
            } else if (typeButton.equals(BottomFragmentImage.ButtonType.WHITE)) {
                createKotlinFunction0(secondBtnFunction).invoke();
            }
        };
    }

    private BottomFragmentImage.OnCallBackBottomImage onCallBackBottomImageReversalDialog() {
        return typeButton -> {
            if (typeButton.equals(BottomFragmentImage.ButtonType.BLUE)) {
                gotoTransactionLimitInformationActivity();
            } else if (typeButton.equals(BottomFragmentImage.ButtonType.WHITE)) {
                gotoTransactionLimitInformationActivityCancel();
            }
        };
    }

    Runnable firstBtnFunction = () -> {
        gotoTransactionLimitInformationActivity();
    };

    Runnable secondBtnFunction = () -> {
        gotoTransactionLimitInformationActivityCancel();
    };

    @Override
    public void onExceptionNoBackAction(String message) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
            GeneralHelper.showBottomDialog(this, message);
        else {
            showSnackbarErrorMessageRevamp(message, ALERT_ERROR, this, false);
            isOnErrorSnackBarShow(true);
        }
    }

    // convert function java to kotlin (Unit)
    public static Function0<Unit> createKotlinFunction0(Runnable action) {
        return () -> {
            action.run();
            return Unit.INSTANCE;
        };
    }

    private void gotoTransactionLimitInformationActivity() {
        Intent newIntent = new Intent(this, TransactionLimitInformationActivity.class);
        startActivity(newIntent);
    }

    private void gotoTransactionLimitInformationActivityCancel() {
        setResult(RESULT_FIRST_USER);
        finish();
    }

    // Define actions for submit and cancel buttons in the blocked account dialog
    public Runnable submitBtnAction = () -> {
        FormLupaPasswordActivity.launchIntent(this, true);
    };

    public Runnable cancelBtnAction = () -> {};

    /**
     * method untuk mengamankan activity dari screenshoot
     *
     * @param isAntiScreenShots Boolean enable or disable anti screenshoot
     */
    public void setAntiScreenShoots(boolean isAntiScreenShots) {
        if (GeneralHelper.isProd() && isAntiScreenShots)
            getWindow().setFlags(
                    WindowManager.LayoutParams.FLAG_SECURE,
                    WindowManager.LayoutParams.FLAG_SECURE);
    }

    public void setAccessibilityServicesProtection(boolean isActivated) {
        if (!isActivated) return;
        boolean hasSuspiciousAccessibilityServices = GeneralHelper.hasSuspiciousAccessibilityServices(this);
        if (!hasSuspiciousAccessibilityServices) return;
        FragmentBottomDialog fragmentBottomDialog = new FragmentBottomDialog(
                this,
                Constant.IMAGE_ACCESSIBILITY_SERVICE_DETECTED,
                GeneralHelper.getString(R.string.warning_a11y_service_title),
                String.format(
                        GeneralHelper.getString(R.string.warning_a11y_service_description),
                        Constant.CALL_CONTACT_BRI
                ),
                Constant.IMAGE_ACCESSIBILITY_SERVICE_DETECTED,
                false,
                () -> {
                    finishAffinity();
                    goToAccessibilitySettings();
                },
                GeneralHelper.getString(R.string.warning_a11y_service_btn_txt)
        );
        fragmentBottomDialog.setCancelable(false);
        fragmentBottomDialog.show(getSupportFragmentManager(), "");
    }

    public void goToAccessibilitySettings() {
        Intent intent = new Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(intent);
    }

    public void updateWidget(Context context) {
        // Send broadcast to update the widget
        Intent intent = new Intent(context, BrimoAppsWidget.class);
        intent.setAction(UPDATE_WIDGET);
        context.sendBroadcast(intent);
    }

    public void showAlertPermission(String msg) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setMessage(msg)
                .setPositiveButton(GeneralHelper.getString(R.string.ok), (dialog, which) -> {
                    Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                    Uri uri = Uri.fromParts("package", getPackageName(), null);
                    intent.setData(uri);
                    startActivity(intent);
                })
                .setNegativeButton(GeneralHelper.getString(R.string.batal2), (dialog, which) -> {
                            dialog.dismiss();
                            finish();
                        }
                )
                .setCancelable(false)
                .show();
    }

    /**
     * Method untuk menampilkan dialog message ketika gps tidak aktif
     */
    public void showDialogEnableLocation(){
        DialogExitCustom dialogExitCustom = new DialogExitCustom(()->{
            Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
            startActivity(intent);
        }, GeneralHelper.getString(R.string.txt_check_gps_title_dialog), GeneralHelper.getString(R.string.txt_check_gps_subtitle_dialog),
                GeneralHelper.getString(R.string.Cancel),GeneralHelper.getString(R.string.Pengaturan_txt));
        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
        ft.add(dialogExitCustom, null);
        ft.commitAllowingStateLoss();
    }

    /**
     * Method untuk menampilkan dialog message ketika gps tidak aktif
     */
    public void showDialogEnableLocationCeria(DialogExitCustom.DialogClickYesNoListener dialogClickYesNoListener,
                                              String title, String subTitle){
        DialogExitCustom dialogNotice = new DialogExitCustom(dialogClickYesNoListener,
                title,
                subTitle,
                GeneralHelper.getString(R.string.txt_exit),
                GeneralHelper.getString(R.string.Pengaturan_txt));
        FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
        ft.add(dialogNotice, null);
        ft.commitAllowingStateLoss();

    }

    protected void recreatedActivity(){
        Intent intent = getIntent();
        intent.putExtra(Constant.IS_CHANGE_LANGUAGE, true);
        setIntent(intent);
        recreate();
    }

    public void goToMaintenanceAlertView(MaintenanceAlert response) {
        Gson gson = new Gson();
        String responseJson = gson.toJson(response);
        Intent newIntent = new Intent(this, AlertMaintenanceActivity.class);
        newIntent.putExtra(Constant.TAG_CONTENT, responseJson);
        newIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(newIntent);
        finish();
    }

    protected void requestOnboardingPermissions(PermissionCallback callback) {
        String[] permissions;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permissions = onboardingPermissions_33;
        } else {
            permissions = onboardingPermissions_below33;
        }

        boolean allGranted = checkAllPermissions(permissions);
        if (allGranted) {
            callback.onPermissionGranted();
        } else {
            permissionCallbacks.put(PERMISSIONS_ONBOARDING, callback);
            ActivityCompat.requestPermissions(this, permissions, PERMISSIONS_ONBOARDING);
        }
    }

    private final Map<Integer, PermissionCallback> permissionCallbacks = new HashMap<>();


    public interface PermissionCallback {
        void onPermissionGranted();
        void onPermissionDenied();
    }

    @RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
    protected String[] onboardingPermissions_33 = {
            Manifest.permission.READ_MEDIA_AUDIO,
            Manifest.permission.CAMERA,
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.POST_NOTIFICATIONS,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
    };

    protected String[] onboardingPermissions_below33 = {
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.CAMERA,
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
    };

    private boolean checkAllPermissions(String[] permissions) {
        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(this, permission) !=
                    PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }
}